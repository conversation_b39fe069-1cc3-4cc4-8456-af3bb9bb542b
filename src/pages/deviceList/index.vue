<template>
	<view class="container">
		<view class="header">
			<text class="title">蓝牙设备列表</text>
			<button class="btn-refresh" @click="handlePullDownRefresh" :disabled="refreshing">
				{{ refreshing ? '搜索中...' : '刷新' }}
			</button>
		</view>

		<view class="device-list">
			<view v-if="devicesList.length === 0" class="empty-tip">
				<text>正在搜索附近的蓝牙设备...</text>
			</view>
			
			<view 
				v-for="(device, index) in devicesList" 
				:key="device.deviceId" 
				class="device-card"
				@click="onTapDevice(device)"
			>
				<view class="device-header">
					<text class="device-name">{{ device.name || device.localName || '未知设备' }}</text>
					<view class="device-status">
						<text v-if="device.beacon" class="status-beacon">信标</text>
						<text v-else-if="!device.connectable" class="status-unconnectable">不可连接</text>
						<text v-else class="status-connectable">可连接</text>
					</view>
				</view>
				
				<view class="device-info">
					<view class="info-row">
						<text class="info-label">设备ID:</text>
						<text class="info-value">{{ device.deviceId.substring(0, 8) }}...</text>
					</view>
					<view class="info-row" v-if="device.RSSI">
						<text class="info-label">信号强度:</text>
						<text class="info-value">{{ device.RSSI }}dBm</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refreshing: false,
			devicesList: [],
			loadingProps: { size: '50rpx' }
		}
	},
	
	onLoad() {
		// 检查微信版本和GPS状态
		this.checkEnvironment();
	},
	
	onShow() {
		// 初始化蓝牙并开始搜索
		this.initBluetooth();
	},
	
	onHide() {
		// 停止搜索
		this.stopDiscovery();
	},
	
	onPullDownRefresh() {
		this.handlePullDownRefresh();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1500);
	},
	
	methods: {
		// 检查环境
		checkEnvironment() {
			// 检查是否在微信小程序环境
			// #ifndef MP-WEIXIN
			uni.showModal({
				title: '提示',
				content: '蓝牙功能仅在微信小程序中可用',
				showCancel: false,
				success: () => {
					uni.navigateBack();
				}
			});
			return;
			// #endif
			
			// 检查微信版本
			const systemInfo = uni.getSystemInfoSync();
			console.log('系统信息:', systemInfo);
		},
		
		// 初始化蓝牙
		initBluetooth() {
			// #ifdef MP-WEIXIN
			wx.openBluetoothAdapter({
				success: () => {
					console.log('蓝牙适配器初始化成功');
					this.startDiscovery();
				},
				fail: (err) => {
					console.error('蓝牙适配器初始化失败:', err);
					uni.showModal({
						title: '蓝牙初始化失败',
						content: '请确保手机蓝牙已开启，然后重试',
						showCancel: false
					});
				}
			});
			// #endif
		},
		
		// 开始搜索设备
		startDiscovery() {
			// #ifdef MP-WEIXIN
			wx.startBluetoothDevicesDiscovery({
				allowDuplicatesKey: true,
				success: () => {
					console.log('开始搜索蓝牙设备');
					this.devicesList = [];
					
					// 监听发现设备
					wx.onBluetoothDeviceFound((res) => {
						this.handleFoundDevices(res.devices);
					});
					
					// 30秒后停止搜索
					setTimeout(() => {
						this.stopDiscovery();
					}, 30000);
				},
				fail: (err) => {
					console.error('开始搜索失败:', err);
					uni.showToast({
						title: '搜索失败，请重试',
						icon: 'none'
					});
				}
			});
			// #endif
		},
		
		// 停止搜索
		stopDiscovery() {
			// #ifdef MP-WEIXIN
			wx.stopBluetoothDevicesDiscovery();
			wx.offBluetoothDeviceFound();
			// #endif
		},
		
		// 处理发现的设备
		handleFoundDevices(devices) {
			const devicesList = [...this.devicesList];
			const deviceIds = devicesList.map(d => d.deviceId);
			
			devices.forEach(device => {
				// 过滤有名称的设备
				if (device.name || device.localName) {
					const index = deviceIds.indexOf(device.deviceId);
					if (index >= 0) {
						// 更新现有设备
						devicesList[index] = device;
					} else {
						// 添加新设备
						devicesList.push(device);
						deviceIds.push(device.deviceId);
					}
				}
			});
			
			this.devicesList = devicesList;
		},
		
		// 下拉刷新
		handlePullDownRefresh() {
			this.refreshing = true;
			this.stopDiscovery();
			setTimeout(() => {
				this.startDiscovery();
				this.refreshing = false;
			}, 1000);
		},
		
		// 点击设备
		onTapDevice(device) {
			if (device.beacon || !device.connectable) {
				uni.showToast({
					title: '该设备不可连接',
					icon: 'none'
				});
				return;
			}
			
			// 显示连接中
			uni.showLoading({
				title: '正在连接...'
			});
			
			// #ifdef MP-WEIXIN
			wx.createBLEConnection({
				deviceId: device.deviceId,
				timeout: 10000,
				success: () => {
					uni.hideLoading();
					uni.showToast({
						title: '连接成功',
						icon: 'success'
					});
					
					// 跳转到UUID列表页面
					const query = `deviceId=${device.deviceId}&deviceName=${encodeURIComponent(device.name || device.localName)}`;
					uni.navigateTo({
						url: `/pages/uuidList/index?${query}`
					});
				},
				fail: (err) => {
					uni.hideLoading();
					console.error('连接失败:', err);
					uni.showToast({
						title: '连接失败，请重试',
						icon: 'none'
					});
				}
			});
			// #endif
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.btn-refresh {
	background-color: #1976D2;
	color: #fff;
	font-size: 28rpx;
	padding: 16rpx 30rpx;
	border-radius: 10rpx;
}

.device-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.empty-tip {
	text-align: center;
	padding: 60rpx 0;
	color: #999;
	font-size: 28rpx;
}

.device-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.device-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.device-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.device-status {
	font-size: 24rpx;
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
}

.status-beacon {
	background-color: #fff7e6;
	color: #ff9800;
}

.status-unconnectable {
	background-color: #ffe6e6;
	color: #f44336;
}

.status-connectable {
	background-color: #e6f7e6;
	color: #07c160;
}

.device-info {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.info-row {
	display: flex;
	justify-content: space-between;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}
</style>
