<template>
	<view class="page">
		<view class="deviceList-header">
			<text>附近设备</text>
		</view>

		<!-- 使用 uni-app 的下拉刷新组件 -->
		<scroll-view
			class="deviceList-content"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="handlePullDownRefresh"
			@refresherrestore="onRefreshRestore"
		>
			<!-- 设备卡片列表 -->
			<view
				v-for="(device, index) in devicesList"
				:key="device.deviceId || index"
				class="deviceCard-wrap"
				@click="onTapDevice(device)"
			>
				<view class="device-info">
					<view class="device-name">
						<view class="name">{{ device.name || device.localName || 'Unknown Device' }}</view>
						<view v-if="device.beacon" class="beacon">iBeacon</view>
					</view>
					<view class="device-id">DeviceId：{{ device.deviceId }}</view>
					<view class="device-id">UUID: {{ (device.advertisServiceUUIDs && device.advertisServiceUUIDs[0]) || null }}</view>
				</view>
				<view class="device-rssi">RSSI：{{ device.RSSI }}</view>
			</view>

			<!-- 底部间距 -->
			<view style="height: 120rpx"></view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refreshing: false,
			devicesList: [],
			services: [],
			loadingProps: { size: '50rpx' }
		}
	},

	// 私有变量
	_deviceIdsList: [],
	_discoveryTimer: null,
	_discoveryTimeout: 30000, // 搜索设备超时时间，单位ms

	onLoad() {
		// 检查微信版本和GPS状态
		this.checkWxVersion();
		this.checkGPSState();
	},

	async onShow() {
		// 初始化小程序蓝牙
		// #ifdef MP-WEIXIN
		wx.openBluetoothAdapter({
			success: () => {
				wx.setStorageSync('bluetoothAdapterState', true);
			},
			fail: (err) => {
				wx.setStorageSync('bluetoothAdapterState', false);
				this.handleErrno(err.errno);
			},
			complete: () => {
				// 监听蓝牙适配器
				wx.offBluetoothAdapterStateChange();
				wx.onBluetoothAdapterStateChange(this.handleBluetoothAdapterStateChange);

				// 监听蓝牙连接
				wx.offBLEConnectionStateChange();
				wx.onBLEConnectionStateChange(this.handleBLEConnectionStateChange);

				// 开始搜索附近设备
				this.onDevicesDiscovery();
			}
		});
		// #endif
	},

	onHide() {
		clearTimeout(this._discoveryTimer);
		// #ifdef MP-WEIXIN
		wx.offBluetoothDeviceFound();
		wx.stopBluetoothDevicesDiscovery();
		// #endif
	},

	methods: {
		// 检查微信版本
		checkWxVersion() {
			// #ifdef MP-WEIXIN
			const systemInfo = uni.getSystemInfoSync();
			console.log('系统信息:', systemInfo);
			// #endif
		},

		// 检查GPS状态
		checkGPSState() {
			// GPS状态检查逻辑
			console.log('检查GPS状态');
		},

		// 开启搜索附近设备
		onDevicesDiscovery() {
			// #ifdef MP-WEIXIN
			if (wx.getStorageSync('bluetoothAdapterState')) {
				wx.startBluetoothDevicesDiscovery({
					allowDuplicatesKey: true, // 重复上报设备
					powerLevel: 'height',
					interval: 1000,
					success: () => {
						this._deviceIdsList = [];
						this.devicesList = [];
						// 发现设备
						wx.onBluetoothDeviceFound(this.handleFoundBluetoothDevices);
						// 超时关闭搜索
						this._discoveryTimer = setTimeout(() => {
							this.offDevicesDiscovery();
						}, this._discoveryTimeout);
					},
					fail: (err) => {
						console.error(err);
					}
				});
			}
			// #endif
		},

		// 下拉刷新
		handlePullDownRefresh() {
			this.refreshing = true;
			setTimeout(() => {
				this.refreshing = false;
			}, 1500);
			this.offDevicesDiscovery();
			this.onDevicesDiscovery();
		},

		// 刷新恢复
		onRefreshRestore() {
			this.refreshing = false;
		},

		// 蓝牙适配器状态改变
		handleBluetoothAdapterStateChange(res) {
			// #ifdef MP-WEIXIN
			uni.hideLoading();
			const { available } = res;
			const originState = wx.getStorageSync('bluetoothAdapterState');
			wx.setStorageSync('bluetoothAdapterState', available);
			if (!available) {
				this.offDevicesDiscovery();
				wx.showLoading({ title: "请打开手机蓝牙", mask: true });
			} else if (!originState) {
				this.onDevicesDiscovery();
			}
			// #endif
		},

		// BLE蓝牙连接状态改变
		async handleBLEConnectionStateChange(res) {
			// #ifdef MP-WEIXIN
			const { connected, deviceId } = res;
			if (connected) {
				await wx.showToast({ title: '连接成功', icon: 'success' });
				wx.setStorageSync('connectedDeviceId', deviceId);
			} else {
				await wx.showToast({ title: '已断开连接', icon: 'none' });
				wx.removeStorageSync('connectedDeviceId');
			}
			// #endif
		},

		// 过滤设备
		filterDevices(devices) {
			return devices.filter(d => {
				this.checkBeacon(d) && (d.beacon = true);
				return d.name && d.localName;
			});
		},

		// 检查是否为信标设备
		checkBeacon(device) {
			// 简单的信标检查逻辑
			return device.advertisData && device.advertisData.length > 0;
		},

		// 搜索附近设备回调
		handleFoundBluetoothDevices({ devices }) {
			const devicesList = [...this.devicesList];
			const { _deviceIdsList = [] } = this;
			devices = this.filterDevices(devices);

			for (let device of devices) {
				const { deviceId } = device;
				const index = _deviceIdsList.indexOf(deviceId);
				if (index < 0) {
					_deviceIdsList.push(deviceId);
					devicesList.push(device);
				} else {
					devicesList.splice(index, 1, device);
				}
			}
			this.devicesList = devicesList;
		},

		// 关闭搜索附近设备
		offDevicesDiscovery() {
			clearTimeout(this._discoveryTimer);
			// #ifdef MP-WEIXIN
			wx.offBluetoothDeviceFound();
			wx.stopBluetoothDevicesDiscovery();
			// #endif
		},

		// 点击设备配对连接
		async onTapDevice(device) {
			const { deviceId, name: localName, beacon, connectable } = device;

			// #ifdef MP-WEIXIN
			if (beacon || !connectable || !wx.getStorageSync('bluetoothAdapterState')) return;

			try {
				wx.showLoading({ title: '正在连接...' });

				// 创建BLE连接
				await wx.createBLEConnection({
					deviceId,
					timeout: 10000
				});

				// 连接成功后，读取服务列表
				const { services } = await wx.getBLEDeviceServices({ deviceId });

				// HC系列蓝牙设备直接进入串口收发页面
				const GeneralServiceUUID = '0000FFE0-0000-1000-8000-00805F9B34FB';
				const GeneralCharacteristicUUID = '0000FFE1-0000-1000-8000-00805F9B34FB';
				const HC02ServiceUUID = '0000FFE0-0000-1000-8000-00805F9B34FB';
				const HC02CharacteristicWriteUUID = '0000FFE1-0000-1000-8000-00805F9B34FB';

				for (let service of services.filter(({ uuid }) => [GeneralServiceUUID, HC02ServiceUUID].includes(uuid))) {
					const { characteristics } = await wx.getBLEDeviceCharacteristics({ deviceId, serviceId: service.uuid });
					for (let characteristic of characteristics.filter(({ uuid }) => [GeneralCharacteristicUUID, HC02CharacteristicWriteUUID].includes(uuid))) {
						const query = this.transToQuery({
							origin: 'deviceList',
							deviceId,
							deviceName: localName,
							serviceId: service.uuid,
							characteristicId: characteristic.uuid,
							...characteristic.properties
						});
						wx.hideLoading();
						return wx.navigateTo({
							url: `/pages/comm/index?${query}`
						});
					}
				}

				// 非HC系列蓝牙设备进入UUID选择页面
				wx.hideLoading();
				const query = this.transToQuery({
					deviceId,
					deviceName: localName
				});
				wx.navigateTo({
					url: `/pages/uuidList/index?${query}`
				});
			} catch (err) {
				wx.hideLoading();
				if (err.errno) {
					this.handleErrno(err.errno);
				} else {
					console.error(err);
					uni.showToast({
						title: '连接失败，请重试',
						icon: 'none'
					});
				}
			}
			// #endif
		},

		// 转换为查询字符串
		transToQuery(obj) {
			return Object.keys(obj).map(key => `${key}=${encodeURIComponent(obj[key])}`).join('&');
		},

		// 错误处理
		handleErrno(errno) {
			console.error('蓝牙错误:', errno);
			uni.showToast({
				title: '蓝牙操作失败',
				icon: 'none'
			});
		}
	}
}
</script>

<style>
/* 页面样式 */
.page {
	width: 100vw;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 头部样式 */
.deviceList-header {
	padding: 0 40rpx;
	height: 80rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-sizing: border-box;
	background-color: #fff;
	font-size: 30rpx;
}

/* 内容区域样式 */
.deviceList-content {
	width: 100vw;
	height: calc(100vh - 80rpx);
	padding: 20rpx;
	box-sizing: border-box;
}

/* 设备卡片样式 */
.deviceCard-wrap {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 40rpx;
	width: 100%;
	height: 180rpx;
	border-radius: 12rpx;
	background-color: #fff;
	box-sizing: border-box;
}

/* 设备名称样式 */
.device-name {
	display: flex;
	align-items: center;
	font-size: 30rpx;
	font-weight: bold;
}

/* 信标标签样式 */
.device-name .beacon {
	margin-left: 15rpx;
	padding: 3rpx 8rpx;
	background-color: yellowgreen;
	border-radius: 5rpx;
	font-size: 20rpx;
	color: #fff;
}

/* 设备ID样式 */
.device-id {
	font-size: 23rpx;
	font-weight: light;
	line-height: 35rpx;
	color: #999;
}

/* RSSI信号强度样式 */
.device-rssi {
	font-size: 25rpx;
}
</style>
