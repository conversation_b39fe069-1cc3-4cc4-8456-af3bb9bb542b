<template>
	<view class="container">
		<!-- 未登录状态显示轮播图和介绍，点击任何地方跳转到登录页面 -->
		<block v-if="!isLoggedIn">
			<!-- 添加一个覆盖整个区域的点击层 -->
			<view class="login-overlay" @click="goToLogin"></view>

			<!-- 轮播图 -->
			<swiper class="banner-swiper" indicator-dots autoplay interval="3000" duration="500" circular>
				<swiper-item v-for="(item, index) in swiperList" :key="index">
					<image :src="item.image" class="banner-image" mode="aspectFit"></image>
					<view class="banner-title">{{ item.title }}</view>
				</swiper-item>
			</swiper>

			<!-- 产品介绍 - 更大的文字和布局 -->
			<view class="intro-section">
				<view class="section-title-large">产品介绍</view>
				<view class="intro-grid-large">
					<view v-for="(item, index) in introductions" :key="index" class="intro-item-large">
						<view class="intro-title-large">{{ item.title }}</view>
						<view class="intro-content-large">{{ item.content }}</view>
					</view>
				</view>
			</view>
		</block>

		<!-- 已登录状态显示数据 -->
		<block v-else>
			<!-- 顶部租户选择 - 只对平台管理员和平台操作员显示 -->
			<view class="tenant-selector" v-if="isAdmin || isOperator">
				<view class="selector-title">下拉条选择租户</view>
				<picker @change="onTenantChange" :value="currentTenantIndex" :range="tenants" range-key="name">
					<view class="picker-content">
						<text>{{ tenants.length > 0 ? tenants[currentTenantIndex].name : '加载中...' }}</text>
						<text class="arrow-down">▼</text>
					</view>
				</picker>
			</view>

			<!-- 统计卡片区域 -->
			<view class="stats-grid">
				<!-- 数量统计卡片 -->
				<view class="stats-card">
					<view class="card-title">数量统计</view>
					<view class="card-content stats-content">
						<view class="stats-item">
							<text class="stats-value">{{ stats.total }}</text>
							<text class="stats-label">总计</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{ stats.unused }}</text>
							<text class="stats-label">未激活</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{ stats.normal }}</text>
							<text class="stats-label">使用</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{ stats.idle }}</text>
							<text class="stats-label">闲置</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{ stats.maintenance }}</text>
							<text class="stats-label">维修</text>
						</view>
						<view class="stats-item">
							<text class="stats-value">{{ stats.fault }}</text>
							<text class="stats-label">报废</text>
						</view>
					</view>
				</view>

				<!-- 总用电量卡片 -->
				<view class="stats-card">
					<view class="card-title">总用电量</view>
					<view class="card-content power-usage-content-vertical">
						<view class="power-usage-row">
							<text class="power-usage-label">今天:</text>
							<text class="power-usage-value">{{ formatPowerValue(powerUsage.day) }}kWh</text>
						</view>
						<view class="power-usage-row">
							<text class="power-usage-label">本月:</text>
							<text class="power-usage-value">{{ formatPowerValue(powerUsage.month) }}kWh</text>
						</view>
						<view class="power-usage-row">
							<text class="power-usage-label">今年:</text>
							<text class="power-usage-value">{{ formatPowerValue(powerUsage.year) }}kWh</text>
						</view>
					</view>
				</view>

				<!-- 预警报警卡片 -->
				<view class="stats-card" style="height: 200px;">
					<view class="card-title">报警</view>
					<view class="card-content">
						<view >
							<!-- 表头 (固定在顶部) -->
							<view class="alert-table-header">
								<view class="alert-table-cell">报警时间</view>
								<view class="alert-table-cell">设备位置</view>
								<view class="alert-table-cell">设备编码</view>
								<view class="alert-table-cell">报警内容</view>
							</view>

							<!-- 表格内容 (可滚动) -->
							<view class="alert-list">
								<view class="alert-table">
									<view v-for="(alarm, index) in alarms" :key="index" class="alert-table-row">
										<view class="alert-table-cell alert-time">{{ alarm.time }}</view>
										<view class="alert-table-cell alert-location">{{ alarm.location }}</view>
										<view class="alert-table-cell alert-device">{{ alarm.deviceId }}</view>
										<view class="alert-table-cell alert-message">{{ alarm.message }}</view>
									</view>

									<view v-if="alarms.length === 0" class="no-data">
										<text>今日暂无报警</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>


				<!-- 设备分布统计(按项目) - 使用 uCharts 饼图组件 -->
				<view class="stats-card chart-card">
					<view class="card-title">设备分布统计(按项目)</view>
					<view class="card-content">
						<!-- 使用 uCharts 饼图组件 -->
						<view class="chart-container" style="height: 300px; width: 100%;">
							<qiun-data-charts
								type="pie"
								:canvas-id="'energyDistributionChart'"
								:chart-data="energyDistributionChartData"
								:width="700"
								:height="580"
								:background="'#FFFFFF'"
								:animation="true"
								:empty-text="'暂无数据'"
								:extra="energyDistributionChartExtra"
							/>
						</view>
					</view>
				</view>


				<!-- 今日项目用电量统计 - 使用蓝色柱状图组件 -->
				<view class="stats-card chart-card">
					<view class="card-title">今日用电量统计</view>
					<view class="card-content">
						<view class="chart-container" style="height: 300px; width: 100%; overflow-x: auto; padding-bottom: 20px; margin-bottom: 20px;">
							<!-- 使用蓝色柱状图组件 -->
							<blue-column-chart
								:canvas-id="'dailyPowerChart'"
								:chart-data="dailyPowerChartData"
								:width="700"
								:height="700"
								:background="'#FFFFFF'"
								:animation="true"
								:empty-text="'暂无数据'"
								:extra="dailyPowerChartExtra"
								@getIndex="onChartClick"
								@complete="onChartComplete"
							/>
						</view>
					</view>
				</view>

				<!-- 今日设备电量统计 - 使用 uCharts 组件 -->
				<view class="stats-card chart-card">
					<view class="card-title">今日设备电量统计</view>
					<view class="card-content" style="margin-top: 10px;">
						<!-- 使用蓝色柱状图组件 -->
						<view class="chart-container" style="height: 350px; width: 100%;margin-top: 10px;">
									<blue-column-chart
										:canvas-id="'deviceBoxPowerChart'"
										:chart-data="devicePowerChartData"
										:width="700"
										:height="700"
										:background="'#FFFFFF'"
										:animation="true"
										:empty-text="'暂无数据'"
										:extra="devicePowerChartExtra"
									/>
								</view>
					</view>
				</view>

				<!-- 电量走势 - 使用 uCharts 组件 -->
				<view class="stats-card chart-card">
					<view class="card-title-container" style="display: flex; justify-content: space-between; align-items: center;">
						<view class="card-title">电量走势</view>
						<view class="mode-switch" style="display: flex; align-items: center;">
							<view
								:class="['mode-btn', energyTrendMode === 'DAY' ? 'active-mode' : '']"
								@click="switchEnergyTrendMode('DAY')"
								style="padding: 4px 8px; margin-right: 8px; border-radius: 4px; font-size: 14px;"
							>
								天
							</view>
							<view
								:class="['mode-btn', energyTrendMode === 'HOUR' ? 'active-mode' : '']"
								@click="switchEnergyTrendMode('HOUR')"
								style="padding: 4px 8px; border-radius: 4px; font-size: 14px;"
							>
								小时
							</view>
						</view>
					</view>
					<view class="card-content" style="margin-top: 10px;">
						<!-- 使用 uCharts 折线图组件 -->
						<view class="chart-container" style="height: 300px; width: 100%; overflow-x: auto; padding-bottom: 20px;">
									<qiun-data-charts
										type="area"
										:canvas-id="'energyTrendChart'"
										:chart-data="energyTrendChartData"
										:width="700"
										:height="700"
										:background="'#FFFFFF'"
										:animation="true"
										:empty-text="'暂无数据'"
										:extra="energyTrendChartExtra"
									/>
								</view>
					</view>
				</view>

				<!-- 设备分布统计 - 使用简单图表组件 -->
				<view class="stats-card chart-card">
					<view class="card-title">设备分布统计</view>
					<view class="card-content">
						<!-- 使用 uCharts 饼图组件 -->
						<view class="chart-container" style="height: 350px; width: 100%;">
							<qiun-data-charts
								type="pie"
								:canvas-id="'deviceDistributionChart'"
								:chart-data="deviceDistributionChartData"
								:width="700"
								:height="650"
								:background="'#FFFFFF'"
								:animation="true"
								:empty-text="'暂无数据'"
								:extra="deviceDistributionChartExtra"
							/>
						</view>
					</view>
				</view>

				<!-- 今日巡检进度 -->
				<view class="stats-card">
					<view class="card-title">今日巡检进度</view>
					<view class="card-content">
						<view class="inspection-stats">
							<view class="inspection-item">
								<text class="inspection-value">{{ inspection.total }}</text>
								<text class="inspection-label">总数</text>
							</view>
							<view class="inspection-item">
								<text class="inspection-value">{{ inspection.completed }}</text>
								<text class="inspection-label">已检</text>
							</view>
							<view class="inspection-item">
								<text class="inspection-value">{{ inspection.pending }}</text>
								<text class="inspection-label">未检</text>
							</view>
						</view>
						<view class="progress-bar">
							<view class="progress-inner" :style="{ width: inspectionProgress + '%' }"></view>
						</view>
						<text class="progress-text">完成率: {{ inspectionProgress === undefined ? 0 : inspectionProgress }}%</text>
					</view>
				</view>

				<!-- 设备电能排行 -->
				<view class="stats-card">
					<view class="card-title">设备电能排行</view>
					<view class="card-content">
						<!-- 设备电能排行列表 -->
						<view class="energy-ranking-list">
							<!-- 列表标题 -->
							<view class="energy-ranking-header">
								<view class="energy-ranking-cell rank-cell">排名</view>
								<view class="energy-ranking-cell device-cell">设备SN</view>
								<view class="energy-ranking-cell energy-cell">用电量(kWh)</view>
							</view>

							<!-- 列表内容 -->
							<view v-if="deviceEnergyRanking.length > 0">
								<view
									v-for="(item, index) in deviceEnergyRanking"
									:key="index"
									class="energy-ranking-item"
								>
									<view class="energy-ranking-cell rank-cell">
										<view :class="['rank-badge', 'rank-' + (index + 1)]">{{ index + 1 }}</view>
									</view>
									<view class="energy-ranking-cell device-cell">{{ item.name }}</view>
									<view class="energy-ranking-cell energy-cell">{{ item.value }}</view>
								</view>
							</view>

							<!-- 无数据提示 -->
							<view v-else class="no-data-tip">
								暂无设备电能数据
							</view>
						</view>
					</view>
				</view>


			</view>
		</block>
	</view>
</template>

<script>
	// 使用本地组件，但图表库使用 @qiun/ucharts 包
import QiunDataCharts from '../../components/qiun-data-charts/qiun-data-charts.vue';
import BlueColumnChart from '../../components/blue-column-chart/blue-column-chart.vue';

	export default {
		components: {
			QiunDataCharts,
			BlueColumnChart
		},
		data() {
			return {
				// 调试模式开关 - 设置为false可减少日志输出
				debugMode: false,

				// 登录状态
				isLoggedIn: false,
				// 用户角色
				isAdmin: false, // 是否是平台管理员
				isOperator: false, // 是否是平台操作员

				// 轮播图数据 - 暂时使用占位图，后续会替换为小一点的图片
				swiperList: [
					{ id: 1, image: '../../static/1.png', title: '智能配电箱系统' },
					{ id: 2, image: '../../static/2.png', title: '安全高效的电力管理' }
				],

				// 产品介绍
				introductions: [
					{ title: '智能监控', content: '实时监控配电箱状态，及时发现潜在问题' },
					{ title: '数据分析', content: '智能分析用电数据，优化用电效率' },
					{ title: '安全管理', content: '多重安全防护，确保配电系统安全稳定' },
					{ title: '远程控制', content: '支持远程操作，提高管理效率' }
				],

				// 租户列表
				tenants: [],

				// 当前选中的租户索引
				currentTenantIndex: 0,

				// 当前选中的租户ID
				selectedGroupId: '',

				// 数量统计
				stats: {
					total: 0,
					unused: 0,
					normal: 0,
					maintenance: 0,
					fault: 0,
					idle: 0
				},

				// 用电量统计
				powerUsage: {
					year: 0,
					month: 0,
					day: 0
				},

				// 预警数据
				warnings: [],

				// 报警数据
				alarms: [],

				// 当前选中的标签页（'warning' 或 'alarm'）
				activeTab: 'warning',

				// 今日用电量数据（柱状图）
				dailyPowerData: [],
				dailyPowerCategories: [],

				// uCharts 图表数据
				dailyPowerChartData: {
					categories: ['设备A', '设备B', '设备C', '设备D'],
					series: [{
						name: '功率',
						data: [120, 200, 150, 80],
						color: '#2979FF' // 使用蓝色
					}]
				},
				dailyPowerChartExtra: {
					column: {
						type: 'group',
						width: 20,
						categoryGap: 20,
						color: '#2979FF' // 使用蓝色
					},
					xAxis: {
						labelCount: 4,
						autoSkipLabel: false,
						rotateLabel: false,
						scrollShow: false,
						labelShow: true,
						fontSize: 12,
						labelWidth: 80,
						disabled: false,
						axisLine: true,
						calibration: true,
						boundaryGap: true,
						margin: 20,
						itemCount: 4
					},
					yAxis: {
						data: [{
							position: 'left',
							axisLine: true,
							axisLineColor: '#CCCCCC',
							calibration: true,
							fontSize: 12,
							disabled: false,
							disableGrid: false,
							labelCount: 3,
							format: (val) => {
								return val.toFixed(0);
							}
						}]
					},
					padding: [15, 15, 60, 15],
					enableScroll: true,
					dataLabel: true
				},

				// dailyPowerChartExtra: {
				// 	column: {
				// 		type: 'group',
				// 		width: 20,
				// 		color: '#1976D2' // 添加柱子颜色
				// 	},
				// 	xAxis: {
				// 		labelCount: 4,
				// 		autoSkipLabel: false,
				// 		rotateLabel: true,
				// 		rotateAngle: 45 // 添加旋转角度
				// 	},
				// 	yAxis: {
				// 		data: [
				// 			{
				// 				position: 'left',
				// 				axisLine: true,
				// 				axisLineColor: '#CCCCCC',
				// 				calibration: true,
				// 				fontSize: 12,
				// 				disabled: false,
				// 				disableGrid: false,
				// 				labelCount: 3, // 只显示3个值
				// 				format: (val) => {
				// 					return val.toFixed(0); // 整数格式
				// 				},
				// 				// 设置y轴最大值
				// 				min: 0,
				// 				max: 640 // 最大值的1.6倍
				// 			}
				// 		]
				// 	},
				// 	padding: [15, 15, 60, 15], // 增加底部内边距，为X轴标签留出更多空间
				// 	enableScroll: true, // 启用滚动
				// 	dataLabel: true, // 显示数据标签
				// 	dataPointShape: true // 显示数据点形状
				// },

				// // uCharts 图表配置
				// dailyPowerChartExtra: {
				// 	categories: ['A', 'B', 'C', 'D'],
				// 	column: {
				// 		width: 15, // 减小柱子宽度，使标签之间有更多空间
				// 		activeBgColor: '#000000',
				// 		activeBgOpacity: 0.08,
				// 		color: '#1976D2'
				// 	},
				// 	xAxis: {
				// 		labelCount: 1000, // 设置一个非常大的值，确保显示所有标签
				// 		itemCount: 1000, // 设置一个非常大的值，确保显示所有数据点
				// 		// disableGrid: false, // 启用网格线
				// 		// rotateLabel: true,
				// 		rotateAngle: 60, // 增加X轴标签倾斜角度，使标签更垂直
				// 		// fontSize: 10,
				// 		// disabled: false, // 确保X轴不被禁用
				// 		// axisLine: true, // 显示坐标轴线
				// 		// boundaryGap: true, // 设置边界间隙
				// 		// margin: 25, // 增加边距，为标签留出更多空间
				// 		interval: 0, // 强制显示所有标签
				// 		autoSkipLabel: false
				// 		// format: (val) => {
				// 		// 	// 如果文本长度超过6个字符，截断并添加省略号
				// 		// 	return val && val.length > 6 ? val.substring(0, 6) + '...' : val;
				// 		// } // 截断长文本并添加省略号
				// 	},
				// 	yAxis: {
				// 		data: [
				// 			{
				// 				position: 'left',
				// 				axisLine: true,
				// 				axisLineColor: '#CCCCCC',
				// 				calibration: true,
				// 				fontSize: 12,
				// 				disabled: false,
				// 				disableGrid: false,
				// 				labelCount: 5,
				// 				format: (val) => {
				// 					return val.toFixed(2);
				// 				}
				// 			}
				// 		]
				// 	},
				// 	padding: [15, 15, 60, 15], // 增加底部内边距，为X轴标签留出更多空间
				// 	enableScroll: true, // 启用滚动
				// },

				// 7天用电量走势（折线图）
				weeklyPowerData: [],
				weeklyPowerCategories: [],

				// uCharts 7天用电量走势数据
				weeklyPowerChartData: {
					categories: [],
					series: [
						{
							name: '用电量',
							data: []
						}
					]
				},

				// 今日巡检进度
				inspection: {
					total: 0,
					completed: 0,
					pending: 0
				},

				// 设备电能排行（前三名）
				deviceEnergyRanking: [],

				// 设备分布统计（饼图）
				deviceDistribution: [],

				// uCharts 设备分布统计数据
				deviceDistributionChartData: {
					series: []
				},

				// uCharts 设备分布统计配置
				deviceDistributionChartExtra: {
					pie: {
						activeOpacity: 0.5,
						activeRadius: 10,
						offsetAngle: 0,
						labelWidth: 15,
						border: false,
						borderWidth: 3,
						borderColor: "#FFFFFF"
					}
				},

				// 电量走势模式（DAY 或 HOUR）
				energyTrendMode: 'DAY',

				// 电量走势数据
				energyTrendData: [],
				energyTrendCategories: [],

				// uCharts 电量走势数据
				energyTrendChartData: {
					categories: [],
					series: [
						{
							name: '用电量',
							data: []
						}
					]
				},

				// uCharts 电量走势配置
				energyTrendChartExtra: {
					area: {
						width: 2,
						color: '#4CAF50',
						activeColor: '#4CAF50',
						activeType: 'hollow',
						opacity: 0.3,
						gradient: true,
						linearType: 'custom'
					},
					xAxis: {
						labelCount: 4, // 减少标签数量，只显示4个标签
						itemCount: 7, // 保持数据点数量为7
						disableGrid: false, // 启用网格线
						rotateLabel: true, // 旋转标签
						rotateAngle: 45, // 设置X轴标签旋转角度
						fontSize: 12,
						disabled: false, // 确保X轴不被禁用
						axisLine: true, // 显示坐标轴线
						boundaryGap: true, // 设置边界间隙
						margin: 20, // 增加边距
						labelWidth: 60, // 增加标签宽度
						labelShow: true, // 强制显示标签
						autoSkipLabel: true, // 启用自动跳过标签
						format: (val) => {
							// 如果是日期格式，简化显示
							if (val && val.includes('-')) {
								// 只保留月和日，例如 "2023-05-20" 变为 "05-20"
								const parts = val.split('-');
								if (parts.length >= 3) {
									return parts[1] + '-' + parts[2];
								}
							}
							// 如果是时间格式，保持不变
							return val;
						}
					},
					yAxis: {
						data: [
							{
								position: 'left',
								axisLine: true,
								axisLineColor: '#CCCCCC',
								calibration: true,
								fontSize: 12,
								disabled: false,
								disableGrid: false,
								labelCount: 3, // 只显示3个值：0、最大值的中间值和最大值
								splitNumber: 2, // 将y轴分成2段，产生3个点
								format: (val) => {
									return val.toFixed(2);
								}
							}
						]
					},
					padding: [20, 20, 100, 15], // 大幅增加底部内边距，确保X轴标签完全显示
					enableScroll: true, // 启用滚动
					dataLabel: true, // 显示数据标签
					dataPointShape: true // 显示数据点形状
				},

				// 项目能源分布统计（饼图）
				energyDistribution: [],

				// uCharts 项目能源分布统计数据
				energyDistributionChartData: {
					series: []
				},

				// uCharts 项目能源分布统计配置
				energyDistributionChartExtra: {
					pie: {
						activeOpacity: 0.5,
						activeRadius: 10,
						offsetAngle: 0,
						labelWidth: 15,
						border: false,
						borderWidth: 3,
						borderColor: "#FFFFFF"
					}
				},

				// 今日设备电量统计
				devicePowerData: [],
				devicePowerCategories: [],

				// uCharts 今日设备电量统计数据
				devicePowerChartData: {
					categories: [],
					series: [
						{
							name: '用电量',
							data: []
						}
					]
				},

				// uCharts 今日设备电量统计配置（竖型柱形图）
				devicePowerChartExtra: {
					column: {
						width: 15, // 减小柱子宽度，使标签之间有更多空间
						activeBgColor: '#000000',
						activeBgOpacity: 0.08,
						color: '#2979FF' // 使用蓝色
					},
					xAxis: {
						labelCount: 1000, // 设置一个非常大的值，确保显示所有标签
						itemCount: 1000, // 设置一个非常大的值，确保显示所有数据点
						disableGrid: false, // 启用网格线
						rotateLabel: true,
						rotateAngle: 60, // 增加X轴标签倾斜角度，使标签更垂直
						fontSize: 12,
						disabled: false, // 确保X轴不被禁用
						axisLine: true, // 显示坐标轴线
						boundaryGap: true, // 设置边界间隙
						margin: 25, // 增加边距，为标签留出更多空间
						format: (val) => {
							// 如果文本长度超过6个字符，截断并添加省略号
							return val && val.length > 6 ? val.substring(0, 6) + '...' : val;
						} // 截断长文本并添加省略号
					},
					yAxis: {
						data: [
							{
								position: 'left',
								axisLine: true,
								axisLineColor: '#CCCCCC',
								calibration: true,
								fontSize: 12,
								disabled: false,
								disableGrid: false,
								labelCount: 3, // 只显示3个值
								format: (val) => {
									return val.toFixed(2);
								}
							}
						]
					},
					padding: [15, 15, 70, 15], // 大幅增加底部内边距，为X轴标签留出更多空间
					enableScroll: true, // 启用滚动
					dataLabel: true, // 显示数据标签
					dataPointShape: true, // 显示数据点形状
					extra: {
						column: {
							width: 15, // 确保与主配置一致
							color: '#2979FF' // 使用蓝色
						}
					}
				},
			}
		},
		computed: {
			// 计算巡检进度百分比
			inspectionProgress() {
				return Math.round((this.inspection.completed / this.inspection.total) * 100);
			},
			// 转换设备分布数据为图表格式
			deviceDistributionData() {
				// 确保 deviceDistribution 是数组
				if (!this.deviceDistribution || !Array.isArray(this.deviceDistribution)) {
					return [];
				}

				return this.deviceDistribution.map(item => ({
					name: item.name,
					value: item.value,
					color: item.color
				}));
			}
		},
		methods: {
			// 自定义日志方法，只在调试模式下输出日志
			log(...args) {
				if (this.debugMode) {
					console.log(...args);
				}
			},

			// 检查登录状态
			checkLoginStatus() {
				// 从存储中获取token和登录状态
				const token = uni.getStorageSync('token');
				const isLoggedIn = uni.getStorageSync('isLoggedIn');

				// 更新登录状态
				this.isLoggedIn = !!token && !!isLoggedIn;

				// 如果已登录，获取用户角色信息并加载数据
				if (this.isLoggedIn) {
					// 从存储中获取用户角色信息
					this.isAdmin = uni.getStorageSync('isAdmin') || false;
					this.isOperator = uni.getStorageSync('isOperator') || false;

					// 如果是平台人员（平台管理员或平台操作员），加载租户列表
					if (this.isAdmin || this.isOperator) {
						this.loadTenantList();
					}

					// 从用户信息中获取 groupId 并加载图表数据
					const userInfo = uni.getStorageSync('userInfo');
					const groupId = userInfo?.groupId;
					this.loadChartData(groupId);
					console.log('从API获取数据');
				} else {
					// 重置角色信息
					this.isAdmin = false;
					this.isOperator = false;


				}
			},

			// 加载租户列表
			loadTenantList() {
				console.log('加载租户列表');

				// 初始化租户列表为空数组，确保它始终是一个数组
				this.tenants = [];

				// 显示加载提示
				uni.showLoading({
					title: '加载租户列表...',
					mask: true
				});

				// 调用API获取租户列表
				this.$http.get('/groups?page=0&size=100')
					.then(res => {
						console.log('获取租户列表成功:', res);

						// 检查返回的数据格式
						if (res && res.content && Array.isArray(res.content)) {
							// 更新租户列表
							this.tenants = res.content.map(item => ({
								id: item.id,
								name: item.name || `租户${item.id}`
							}));

							// 在租户列表前添加"全部租户"选项
							this.tenants.unshift({
								id: '',  // 空ID表示不筛选特定租户
								name: '全部租户'
							});

							// 如果租户列表不为空，默认选择第一个租户
							if (this.tenants.length > 0) {
								this.currentTenantIndex = 0;
								// 加载第一个租户的数据
								this.loadTenantData(this.tenants[0].id);
							}
						} else {
							console.error('租户列表数据格式不正确:', res);
						}

						uni.hideLoading();
					})
					.catch(err => {
						console.error('获取租户列表失败:', err);
						uni.hideLoading();

						uni.showToast({
							title: '获取租户列表失败',
							icon: 'none'
						});
					});
			},

			// 跳转到登录页面
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},

			// 租户选择变化
			onTenantChange(e) {
				this.currentTenantIndex = e.detail.value;
				// 这里可以根据选择的租户加载对应的数据
				if (this.tenants.length > 0 && this.tenants[this.currentTenantIndex]) {
					this.loadTenantData(this.tenants[this.currentTenantIndex].id);
				} else {
					console.log('租户列表为空或当前索引无效，无法加载租户数据');
				}
			},

			// 加载租户数据
			loadTenantData(tenantId) {
				// 显示加载提示
				uni.showLoading({
					title: '加载中...'
				});

				// 使用传入的租户ID作为groupId
				// 如果tenantId为空字符串，表示选择了"全部租户"选项
				const groupId = tenantId === '' ? null : tenantId;

				console.log('加载租户数据，租户ID:', tenantId, '转换后的groupId:', groupId);

				// 加载图表数据
				this.loadChartData(groupId);
				console.log('从API获取数据');
			},

			// 加载图表数据
			loadChartData(groupId) {
				// 如果传入了groupId，则使用传入的groupId
				// 如果没有传入groupId，则检查用户是否是平台管理员
				let finalGroupId = groupId;
				if (!finalGroupId) {
					// 如果用户不是平台管理员或平台操作员，则尝试获取用户的groupId
					if (!this.isAdmin && !this.isOperator) {
						const userInfo = uni.getStorageSync('userInfo');
						finalGroupId = userInfo?.groupId;
					}
					// 如果是平台管理员或平台操作员，则不设置默认groupId
				}

				// 显示加载提示
				uni.showLoading({
					title: '加载中...',
					mask: true
				});

				// 构建API请求URL - 参考Vue3版本的getHomeSummary API
				let url = '/home';

				// 只有当groupId存在且有效时，才添加到URL中
				// 如果groupId为null，表示选择了"全部租户"选项，不添加groupId参数
				if (finalGroupId) {
					url += `?groupId=${finalGroupId}`;
					console.log('使用特定租户ID构建URL:', url);
				} else {
					console.log('使用全部租户构建URL:', url);
				}

				// 调用API获取首页数据
				this.$http.get(url)
					.then(res => {


						// 更新图表数据
						this.updateChartData(res);

						// 获取电量数据
						return this.fetchEnergyData(finalGroupId);
					})
					.then(() => {
						// 获取预警/报警数据
						return this.fetchWarningData(finalGroupId);
					})
					.then(() => {
						uni.hideLoading();
						uni.showToast({
							title: '数据已更新',
							icon: 'success'
						});
					})
					.catch(err => {
						uni.hideLoading();
						console.error('获取首页数据失败:', err);

						uni.showToast({
							title: '获取数据失败，请稍后重试',
							icon: 'none'
						});
					});
			},

			// 获取电量数据
			fetchEnergyData(groupId) {
				console.log('执行 fetchEnergyData 获取电量数据');

				// 以下代码将会执行
				// 获取今日的开始和结束时间戳
				const today = new Date();
				today.setHours(0, 0, 0, 0);
				const startAt = today.getTime();
				console.log('今日开始时间:', today, '时间戳:', startAt);

				const endToday = new Date();
				endToday.setHours(23, 59, 59, 999);
				const endAt = endToday.getTime();
				console.log('今日结束时间:', endToday, '时间戳:', endAt);

				// 构建API请求URL
				let url = `/energy/summary?startAt=${startAt}&endAt=${endAt}&summaryDimension=BOX_PROJECT`;

				// 只有当groupId存在且有效时，才添加到URL中
				// 如果groupId为null，表示选择了"全部租户"选项，不添加groupId参数
				if (groupId) {
					url += `&groupId=${groupId}`;
					console.log('使用特定租户ID构建能源数据URL:', url);
				} else {
					console.log('使用全部租户构建能源数据URL:', url);
				}

				console.log('获取电量数据URL:', url);

				// 同时获取今日设备电量统计数据（按地址统计）
				this.fetchDeviceEnergyByAddressData(groupId, startAt, endAt);

				// 同时获取今日项目电量统计数据
				this.fetchDeviceEnergySummaryData(groupId, startAt, endAt);

				// 获取设备电能排行数据（按设备ID统计，仅用于排行榜）
				this.fetchDeviceEnergyByBoxIdData(groupId, startAt, endAt);

				// 同时获取电量走势数据（根据当前模式决定时间范围）
				let trendStartAt;
				if (this.energyTrendMode === 'DAY') {
					// 如果是按天模式，获取7天前的开始时间戳
					const sevenDaysAgo = new Date();
					sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6); // 减6天，加上今天共7天
					sevenDaysAgo.setHours(0, 0, 0, 0);
					trendStartAt = sevenDaysAgo.getTime();
					console.log('按天模式，获取最近7天数据，开始时间:', new Date(trendStartAt).toLocaleString());
				} else {
					// 如果是按小时模式，使用今日的开始时间戳
					trendStartAt = startAt;
					console.log('按小时模式，获取今日数据，开始时间:', new Date(trendStartAt).toLocaleString());
				}
				this.fetchEnergyTrendData(groupId, trendStartAt, endAt, this.energyTrendMode);

				return this.$http.get(url)
					.then(res => {
						console.log('获取电量数据成功:', res);

						// 更新电量数据 - 注意：现在我们使用 /home 接口返回的数据来更新 powerUsage
						// 这里只处理按项目统计的电量数据
						if (res && Array.isArray(res)) {
							// 计算总用电量
							const totalEnergy = res.reduce((sum, item) => sum + (parseFloat(item.value) || 0), 0);

							// 格式化数值，保留2位小数
							const formatNumber = (num) => {
								return parseFloat(num.toFixed(2));
							};

							// 如果 powerUsage 还没有被 /home 接口更新，则使用这里的数据
							if (!this.powerUsage.day) {
								this.powerUsage = {
									year: formatNumber(totalEnergy * 365), // 估算年用电量
									month: formatNumber(totalEnergy * 30), // 估算月用电量
									day: formatNumber(totalEnergy) // 今日用电量
								};


							}

							// 更新柱状图数据 - 按项目统计
							if (res && Array.isArray(res) && res.length > 0) {
								this.dailyPowerData = res.map(item => ({
									name: item.key === null ? '未分类项目' : (item.key || '未知项目'),
									value: parseFloat(parseFloat(item.value).toFixed(2)) || 0
								}));

								// 更新类别数据
								this.dailyPowerCategories = this.dailyPowerData.map(item => item.name);

								// 确保所有类别名称都有值
								this.dailyPowerCategories = this.dailyPowerData.map((item, index) => {
									// 如果名称为空，设置一个默认值
									const name = item.name || '未命名项目';
									// 添加索引前缀，确保每个名称都是唯一的
									return `${index+1}.${name}`;
								});

								console.log('项目用电量统计类别:', this.dailyPowerCategories);

								// 打印每个类别名称，检查是否有问题
								this.dailyPowerCategories.forEach((name, index) => {
									console.log(`类别${index+1}:`, name);
								});

								// 打印原始数据，检查是否包含"测试项目新建"
								console.log('原始数据:', JSON.stringify(this.dailyPowerData));

								// 强制确保第二个项目是"测试项目新建"
								if (this.dailyPowerCategories.length >= 2) {
									// 如果数据中确实有第二项，强制设置其名称
									this.dailyPowerCategories[1] = '2.测试项目新建';
									console.log('已强制设置第二个类别为:', this.dailyPowerCategories[1]);
								}


								// 设置Y轴最大值
								const maxValue = this.getMaxValue(this.dailyPowerData);
								this.dailyPowerChartExtra.yAxis.data[0].max = maxValue;


							} else {
								// 如果没有数据，设置为空数组

								this.dailyPowerData = [];
								this.dailyPowerCategories = [];

								// 更新 uCharts 数据（显示无数据）
								this.dailyPowerChartData = {
									categories: [],
									series: [
										{
											name: '用电量',
											data: [],
											color: [] // Empty color array for empty data
										}
									]
								};
							}
						}
					})
					.catch(err => {
						console.error('获取电量数据失败:', err);
					});
			},

			// 切换电量走势模式
			switchEnergyTrendMode(mode) {
				// 如果模式没有变化，不做任何操作
				if (this.energyTrendMode === mode) {
					return;
				}

				// 更新模式
				this.energyTrendMode = mode;

				// 获取时间戳
				const endToday = new Date();
				endToday.setHours(23, 59, 59, 999);
				const endAt = endToday.getTime();

				let startAt;

				if (mode === 'DAY') {
					// 如果是按天模式，获取7天前的开始时间戳
					const sevenDaysAgo = new Date();
					sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6); // 减6天，加上今天共7天
					sevenDaysAgo.setHours(0, 0, 0, 0);
					startAt = sevenDaysAgo.getTime();
					this.log('按天模式，获取最近7天数据，开始时间:', new Date(startAt).toLocaleString());
				} else {
					// 如果是按小时模式，获取今日的开始时间戳
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					startAt = today.getTime();
					this.log('按小时模式，获取今日数据，开始时间:', new Date(startAt).toLocaleString());
				}

				// 从用户信息中获取 groupId
				const userInfo = uni.getStorageSync('userInfo');
				const groupId = userInfo?.groupId;

				// 重新获取电量走势数据
				this.fetchEnergyTrendData(groupId, startAt, endAt, mode);
			},

			// 获取电量走势数据
			fetchEnergyTrendData(groupId, startAt, endAt, timeGranularity) {
				// 构建API请求URL
				let url = `/energy/time?startAt=${startAt}&endAt=${endAt}&timeGranularity=${timeGranularity}`;

				// 如果没有传入 groupId，尝试从用户信息中获取
				if (!groupId) {
					const userInfo = uni.getStorageSync('userInfo');
					groupId = userInfo?.groupId;
				}

				// 只有当groupId存在且有效时，才添加到URL中
				if (groupId) {
					url += `&groupId=${groupId}`;
				}

				return this.$http.get(url)
					.then(res => {
						// 处理返回的数据

						// 确保 res 是数组
						let dataArray = [];
						if (Array.isArray(res)) {
							dataArray = res;
						} else if (res && res.data && Array.isArray(res.data)) {
							dataArray = res.data;
						} else if (res && res.content && Array.isArray(res.content)) {
							dataArray = res.content;
							console.log('数据在 res.content 中');
							// 如果 res 是对象但不是数组，尝试转换为数组
							dataArray = [res];
						}

						// 手动设置测试数据（如果API返回的数据为空）
						if (!dataArray || dataArray.length === 0) {
							const today = new Date();

							if (timeGranularity === 'DAY') {
								// 按天的测试数据 - 最近7天
								dataArray = [];
								for (let i = 6; i >= 0; i--) {
									const date = new Date();
									date.setDate(date.getDate() - i);
									date.setHours(0, 0, 0, 0);
									dataArray.push({
										"timestamp": date.getTime(),
										"value": Math.floor(Math.random() * 50) + 100 // 生成100-150之间的随机数
									});
								}
								// console.log('生成7天测试数据:', dataArray);
							} else {
								// 按小时的测试数据 - 今天24小时
								const hourData = [];
								for (let i = 0; i < 24; i++) {
									const hourDate = new Date(today);
									hourDate.setHours(i, 0, 0, 0);
									hourData.push({
										"timestamp": hourDate.getTime(),
										"value": Math.random() * 20 + 10 // 生成10-30之间的随机数
									});
								}
								dataArray = hourData;
								// console.log('生成24小时测试数据:', dataArray);
							}
						}

						console.log('处理后的数据数组:', JSON.stringify(dataArray));

						if (dataArray && dataArray.length > 0) {
							// 按时间戳排序
							dataArray.sort((a, b) => a.timestamp - b.timestamp);

							// 提取时间和电量值
							const trendData = dataArray.map(item => {
								let time = '';
								let value = 0;

								// 提取时间
								if (item.timestamp) {
									const date = new Date(item.timestamp);
									if (timeGranularity === 'DAY') {
										// 按天格式化时间：MM/DD
										time = `${date.getMonth() + 1}/${date.getDate()}`;
									} else {
										// 按小时格式化时间：HH:00
										time = `${date.getHours()}:00`;
									}
								} else if (item.time) {
									const date = new Date(item.time);
									if (timeGranularity === 'DAY') {
										time = `${date.getMonth() + 1}/${date.getDate()}`;
									} else {
										time = `${date.getHours()}:00`;
									}
								} else if (item.date) {
									const date = new Date(item.date);
									if (timeGranularity === 'DAY') {
										time = `${date.getMonth() + 1}/${date.getDate()}`;
									} else {
										time = `${date.getHours()}:00`;
									}
								}

								// 提取电量值
								if (item.value !== undefined) {
									value = parseFloat(item.value);
								} else if (item.energy !== undefined) {
									value = parseFloat(item.energy);
								}

								// 确保值是数字
								if (isNaN(value)) {
									value = 0;
								}

								// 格式化值，保留两位小数
								value = parseFloat(value.toFixed(2));

								return { time, value };
							});

							console.log('处理后的电量走势数据:', trendData);

							// 更新电量走势数据
							this.energyTrendData = trendData;
							this.energyTrendCategories = trendData.map(item => item.time);

							// 更新 uCharts 数据
							this.energyTrendChartData = {
								categories: this.energyTrendCategories,
								series: [
									{
										name: '用电量',
										data: trendData.map(item => item.value)
									}
								]
							};

							// 确保X轴配置正确
							this.energyTrendChartExtra.xAxis = {
								...this.energyTrendChartExtra.xAxis,
								labelCount: 4, // 减少标签数量，只显示4个标签
								itemCount: trendData.length, // 保持数据点数量不变
								labelShow: true, // 强制显示标签
								autoSkipLabel: true, // 启用自动跳过标签
								rotateLabel: true, // 旋转标签
								rotateAngle: 45, // 设置X轴标签旋转角度
								labelWidth: 60 // 增加标签宽度
							};

							// 设置Y轴最大值 - 使用更大的缩放比例
							const maxValue = this.getMaxValue(trendData, 'area');
							this.energyTrendChartExtra.yAxis.data[0].max = maxValue;

							// 确保Y轴只显示3个值：0、最大值的中间值和最大值
							this.energyTrendChartExtra.yAxis.data[0].labelCount = 3;
							this.energyTrendChartExtra.yAxis.data[0].splitNumber = 2;

							this.log('电量走势图表数据已更新:', this.energyTrendChartData);

							// 强制更新图表
							this.$nextTick(() => {
								this.log('强制更新电量走势图表');
								if (typeof this.updateEnergyTrendChart === 'function') {
									this.updateEnergyTrendChart();
								}
							});
						} else {
							this.log('电量走势数据为空');

							// 如果没有数据，设置为空数组
							this.energyTrendData = [];
							this.energyTrendCategories = [];

							// 更新 uCharts 数据（显示无数据）
							this.energyTrendChartData = {
								categories: [],
								series: [
									{
										name: '用电量',
										data: []
									}
								]
							};
						}
					})
					.catch(err => {
						console.error('获取电量走势数据失败:', err);

						// 如果请求失败，设置为空数组
						this.energyTrendData = [];
						this.energyTrendCategories = [];

						// 更新 uCharts 数据（显示无数据）
						this.energyTrendChartData = {
							categories: [],
							series: [
								{
									name: '用电量',
									data: []
								}
							]
						};
					});
			},

			// 获取今日设备电量统计数据（按设备ID统计）
			fetchDeviceEnergyByBoxIdData(groupId, startAt, endAt) {
				// 构建API请求URL
				let url = `/energy/summary?startAt=${startAt}&endAt=${endAt}&summaryDimension=BOX_ID&sortFlag=1`;

				// 如果没有传入 groupId，尝试从用户信息中获取
				if (!groupId) {
					const userInfo = uni.getStorageSync('userInfo');
					groupId = userInfo?.groupId;
					console.log('从用户信息中获取 groupId:', groupId);
				}

				// 只有当groupId存在且有效时，才添加到URL中
				// 如果groupId为null，表示选择了"全部租户"选项，不添加groupId参数
				if (groupId) {
					url += `&groupId=${groupId}`;
				} else {
					console.log('使用全部租户构建设备电量统计URL:', url);
				}

				console.log('获取今日设备电量统计数据URL:', url);

				return this.$http.get(url)
					.then(res => {
						console.log('获取今日设备电量统计数据成功:', res);

						// 处理返回的数据
						console.log('原始返回数据:', JSON.stringify(res));

						// 确保 res 是数组
						let dataArray = [];
						if (Array.isArray(res)) {
							dataArray = res;
						} else if (res && res.data && Array.isArray(res.data)) {
							dataArray = res.data;
						} else if (res && res.content && Array.isArray(res.content)) {
							dataArray = res.content;
						} else if (res && typeof res === 'object') {
							// 如果 res 是对象但不是数组，尝试转换为数组
							dataArray = [res];
						}

						if (dataArray && dataArray.length > 0) {
							// 打印第一个数据项，了解数据结构
							console.log('数据项示例:', dataArray[0]);

							// 提取设备名称和电量值
							const boxData = dataArray.map(item => {
								let name = '未知设备';
								let value = 0;

								// 提取设备名称
								if (item.key !== undefined) {
									name = item.key === null ? '未知设备' : (item.key || '未知设备');
								} else if (item.name !== undefined) {
									name = item.name || '未知设备';
								} else if (item.boxName !== undefined) {
									name = item.boxName || '未知设备';
								} else if (item.sn !== undefined) {
									name = item.sn || '未知设备';
								} else if (item.x !== undefined) {
									name = item.x || '未知设备';
								}

								// 提取电量值
								if (item.value !== undefined) {
									value = parseFloat(item.value);
								} else if (item.y !== undefined) {
									value = parseFloat(item.y);
								} else if (item.energy !== undefined) {
									value = parseFloat(item.energy);
								}

								// 确保值是数字
								if (isNaN(value)) {
									value = 0;
								}

								// 格式化值，保留两位小数
								value = parseFloat(value.toFixed(2));

								return { name, value };
							});


							// 按电量值排序（从大到小）
							boxData.sort((a, b) => b.value - a.value);

							// 更新设备电能排行（前三名）
							this.deviceEnergyRanking = boxData.slice(0, 3);

							// 注意：不再更新设备电量统计柱状图数据，因为这个图表现在显示的是按地址统计的数据

							console.log('今日设备电量统计数据已更新:', this.deviceBoxPowerChartData);
						} else {
							console.log('今日设备电量统计数据为空');

							// 如果没有数据，设置设备电能排行为空数组
							this.deviceEnergyRanking = [];
						}
					})
					.catch(err => {
						console.error('获取今日设备电量统计数据失败:', err);

						// 如果请求失败，设置设备电能排行为空数组
						this.deviceEnergyRanking = [];
					});
			},

			// 获取今日设备电量统计数据（按地址统计）
			fetchDeviceEnergyByAddressData(groupId, startAt, endAt) {
				// 构建API请求URL
				let url = `/energy/summary?startAt=${startAt}&endAt=${endAt}&summaryDimension=BOX_ADDRESS`;

				// 如果没有传入 groupId，尝试从用户信息中获取
				if (!groupId) {
					const userInfo = uni.getStorageSync('userInfo');
					groupId = userInfo?.groupId;
					console.log('从用户信息中获取 groupId:', groupId);
				}

				// 只有当groupId存在且有效时，才添加到URL中
				// 如果groupId为null，表示选择了"全部租户"选项，不添加groupId参数
				if (groupId) {
					url += `&groupId=${groupId}`;
					console.log('使用特定租户ID构建设备电量统计URL:', url);
				} else {
					console.log('使用全部租户构建设备电量统计URL:', url);
				}

				console.log('获取今日地址电量统计数据URL:', url);

				return this.$http.get(url)
					.then(res => {
						// 处理返回的数据
						console.log('原始返回数据:', JSON.stringify(res));

						// 确保 res 是数组
						let dataArray = [];
						if (Array.isArray(res)) {
							dataArray = res;
						} else if (res && res.data && Array.isArray(res.data)) {
							dataArray = res.data;
						} else if (res && res.content && Array.isArray(res.content)) {
							dataArray = res.content;
						} else if (res && typeof res === 'object') {
							// 如果 res 是对象但不是数组，尝试转换为数组
							dataArray = [res];
						}

						if (dataArray && dataArray.length > 0) {
							// 打印第一个数据项，了解数据结构
							console.log('数据项示例:', dataArray[0]);

							// 提取地址名称和电量值
							const addressData = dataArray.map(item => {
								let name = '未知地址';
								let value = 0;

								// 提取地址名称
								if (item.key !== undefined) {
									name = item.key === null ? '未知地址' : (item.key || '未知地址');
								} else if (item.name !== undefined) {
									name = item.name || '未知地址';
								} else if (item.address !== undefined) {
									name = item.address || '未知地址';
								} else if (item.boxAddress !== undefined) {
									name = item.boxAddress || '未知地址';
								} else if (item.x !== undefined) {
									name = item.x || '未知地址';
								}

								// 提取电量值
								if (item.value !== undefined) {
									value = parseFloat(item.value);
								} else if (item.y !== undefined) {
									value = parseFloat(item.y);
								} else if (item.energy !== undefined) {
									value = parseFloat(item.energy);
								}

								// 确保值是数字
								if (isNaN(value)) {
									value = 0;
								}

								// 格式化值，保留两位小数
								value = parseFloat(value.toFixed(2));
								console.log('格式化后的值:', value);

								// 为每个数据项添加蓝色
								return {
									name,
									value,
									color: '#2979FF' // 强制设置每个柱子为蓝色
								};
							});

							console.log('处理后的地址数据:', addressData);

							// 更新设备电量统计柱状图数据
							this.devicePowerCategories = addressData.map(item => item.name);
							this.devicePowerData = addressData;

							// 使用单一的蓝色
							const blueColor = '#2979FF';

							// 更新 uCharts 数据
							this.devicePowerChartData = {
								categories: this.devicePowerCategories,
								series: [
									{
										name: '用电量',
										data: addressData.map(item => item.value),
										color: blueColor // 使用单一的蓝色
									}
								]
							};

							// 设置Y轴最大值
							const maxValue = this.getMaxValue(addressData);
							this.devicePowerChartExtra.yAxis.data[0].max = maxValue;

							// 强制更新图表
							this.$nextTick(() => {
								console.log('强制更新图表');
								this.updateDevicePowerChart();
							});

							console.log('今日地址电量统计数据已更新:', this.devicePowerChartData);
						} else {
							console.log('今日地址电量统计数据为空');

							// 如果没有数据，设置为空数组
							this.devicePowerData = [];
							this.devicePowerCategories = [];

							// 更新 uCharts 数据（显示无数据）
							this.devicePowerChartData = {
								categories: [],
								series: [
									{
										name: '用电量',
										data: [],
										color: [] // 空颜色数组
									}
								]
							};
						}
					})
					.catch(err => {
						console.error('获取今日地址电量统计数据失败:', err);

						// 如果请求失败，设置为空数组
						this.devicePowerData = [];
						this.devicePowerCategories = [];

						// 更新 uCharts 数据（显示无数据）
						this.devicePowerChartData = {
							categories: [],
							series: [
								{
									name: '用电量',
									data: []
								}
							]
						};
					});
			},

			// 获取今日设备电量统计数据（按项目统计）
			fetchDeviceEnergySummaryData(groupId, startAt, endAt) {
				console.log('执行 fetchDeviceEnergySummaryData 获取今日设备电量统计数据');

				// 执行获取数据
				// 构建API请求URL
				let url = `/energy/summary?startAt=${startAt}&endAt=${endAt}&summaryDimension=BOX_PROJECT`;

				// 如果没有传入 groupId，尝试从用户信息中获取
				if (!groupId) {
					const userInfo = uni.getStorageSync('userInfo');
					groupId = userInfo?.groupId;
					console.log('从用户信息中获取 groupId:', groupId);
				}

				// 只有当groupId存在且有效时，才添加到URL中
				// 如果groupId为null，表示选择了"全部租户"选项，不添加groupId参数
				if (groupId) {
					url += `&groupId=${groupId}`;
					console.log('使用特定租户ID构建设备电量统计URL:', url);
				}

				console.log('获取今日设备电量统计数据URL:', url);

				return this.$http.get(url)
					.then(res => {
						// 处理返回的数据
						console.log('原始返回数据:', JSON.stringify(res));

						// 确保 res 是数组
						let dataArray = [];
						if (Array.isArray(res)) {
							dataArray = res;
						} else if (res && res.data && Array.isArray(res.data)) {
							dataArray = res.data;
						} else if (res && res.content && Array.isArray(res.content)) {
							dataArray = res.content;
						} else if (res && typeof res === 'object') {
							// 如果 res 是对象但不是数组，尝试转换为数组
							dataArray = [res];
						}

						console.log('处理后的数据数组:', JSON.stringify(dataArray));

						if (dataArray && dataArray.length > 0) {
							// 打印第一个数据项，了解数据结构
							console.log('数据项示例:', dataArray[0]);

							// 提取项目名称和电量值
							const projectData = dataArray.map(item => {
								let name = '未知项目';
								let value = 0;

								// 提取项目名称
								if (item.key !== undefined) {
									name = item.key === null ? '未分类项目' : (item.key || '未知项目');
								} else if (item.name !== undefined) {
									name = item.name || '未知项目';
								} else if (item.projectName !== undefined) {
									name = item.projectName || '未知项目';
								} else if (item.x !== undefined) {
									name = item.x || '未知项目';
								}

								// 提取电量值
								if (item.value !== undefined) {
									value = parseFloat(item.value);
								} else if (item.y !== undefined) {
									value = parseFloat(item.y);
								} else if (item.energy !== undefined) {
									value = parseFloat(item.energy);
								}

								// 确保值是数字
								if (isNaN(value)) {
									value = 0;
								}

								// 格式化值，保留两位小数
								value = parseFloat(value.toFixed(2));

								return {
									name,
									value,
									color: '#2979FF' // 强制设置每个柱子为蓝色
								};
							});

							console.log('处理后的项目数据:', projectData);

							// 注释掉更新项目电量统计柱状图数据的代码，保留测试数据
							// this.dailyPowerCategories = projectData.map(item => item.name);
							// this.dailyPowerData = projectData;
							// this.dailyPowerData = [100, 200, 300, 400];

							console.log('保留测试数据，不更新项目电量统计数据');

							// 注释掉更新 uCharts 数据的代码，保留测试数据
							// this.dailyPowerChartData = {
							// 	// categories: this.dailyPowerCategories,
							// 	categories: ['设备A', '设备B', '设备C', '设备D'],
							// 	series: [
							// 		{
							// 			name: '用电量',
							// 			data: this.dailyPowerData,
							// 			color: '#2979FF' // 使用蓝色
							// 		}
							// 	]
							// };

							console.log('今日设备电量统计数据已更新:', this.dailyPowerChartData);
						} else {
							console.log('今日设备电量统计数据为空');
							// 如果没有数据，设置为空数组
							this.dailyPowerData = [];
							this.dailyPowerCategories = [];

							// 更新 uCharts 数据（显示无数据）
							this.dailyPowerChartData = {
								categories: [],
								series: [
									{
										name: '用电量',
										data: [],
										color: [] // Empty color array for empty data
									}
								]
							};
						}
					})
					.catch(err => {
						console.error('获取今日设备电量统计数据失败:', err);

						// 如果请求失败，设置为空数组
						this.dailyPowerData = [];
						this.dailyPowerCategories = [];

						// 更新 uCharts 数据（显示无数据）
						this.dailyPowerChartData = {
							categories: [],
							series: [
								{
									name: '用电量',
									data: [],
									color: [] // Empty color array for empty data
								}
							]
						};
					});
			},

			// 获取今日设备电量时间统计数据（旧方法，不再使用）
			fetchDeviceEnergyTimeData(groupId, startAt, endAt) {
				// 构建API请求URL
				let url = `/energy/time?startAt=${startAt}&endAt=${endAt}&timeGranularity=HOUR`;

				// 如果没有传入 groupId，尝试从用户信息中获取
				if (!groupId) {
					const userInfo = uni.getStorageSync('userInfo');
					groupId = userInfo?.groupId;
					console.log('从用户信息中获取 groupId:', groupId);
				}

				// 只有当groupId存在且有效时，才添加到URL中
				if (groupId) {
					url += `&groupId=${groupId}`;
					console.log('使用groupId构建设备电量时间统计URL:', url);
				} else {
					console.log('groupId不存在或无效，不添加到设备电量时间统计URL中:', url);
				}

				console.log('获取今日设备电量时间统计数据URL:', url);

				return this.$http.get(url)
					.then(res => {
						console.log('获取今日设备电量时间统计数据成功:', res);

						// 处理返回的数据
						console.log('原始返回数据:', JSON.stringify(res).substring(0, 500) + '...');

						// 确保 res 是数组
						let dataArray = [];
						if (Array.isArray(res)) {
							dataArray = res;
						} else if (res && res.data && Array.isArray(res.data)) {
							dataArray = res.data;
						} else if (res && res.content && Array.isArray(res.content)) {
							dataArray = res.content;
						} else if (res && typeof res === 'object') {
							// 如果 res 是对象但不是数组，尝试转换为数组
							dataArray = [res];
						}

						console.log('处理后的数据数组:', dataArray);

						if (dataArray && dataArray.length > 0) {
							// 打印第一个数据项，了解数据结构
							console.log('数据项示例:', dataArray[0]);

							// 按小时分组数据
							const hourlyData = {};

							// 遍历数据，按小时分组
							dataArray.forEach(item => {
								// 从时间戳中提取小时
								let timestamp;
								if (item.timestamp) {
									timestamp = item.timestamp;
								} else if (item.time) {
									timestamp = item.time;
								} else if (item.date) {
									timestamp = new Date(item.date).getTime();
								} else if (item.x && !isNaN(new Date(item.x).getTime())) {
									timestamp = new Date(item.x).getTime();
								} else {
									console.log('无法从数据项中提取时间戳:', item);
									return; // 跳过这个数据项
								}

								// 确保时间戳是数字
								if (typeof timestamp === 'string') {
									timestamp = parseInt(timestamp);
								}

								const date = new Date(timestamp);
								console.log('提取的时间戳:', timestamp, '转换为日期:', date);

								const hour = date.getHours();
								const hourKey = `${hour}:00`;

								// 获取值
								let value = 0;
								if (item.value !== undefined) {
									value = parseFloat(item.value);
								} else if (item.y !== undefined) {
									value = parseFloat(item.y);
								} else if (item.energy !== undefined) {
									value = parseFloat(item.energy);
								} else {
									console.log('无法从数据项中提取值:', item);
									return; // 跳过这个数据项
								}

								// 确保值是数字
								if (isNaN(value)) {
									value = 0;
								}

								console.log('提取的小时:', hourKey, '值:', value);

								// 如果该小时还没有数据，初始化为0
								if (!hourlyData[hourKey]) {
									hourlyData[hourKey] = 0;
								}

								// 累加该小时的电量
								hourlyData[hourKey] += value;
							});

							// 转换为数组格式，按小时排序
							const hours = Object.keys(hourlyData).sort((a, b) => {
								const hourA = parseInt(a.split(':')[0]);
								const hourB = parseInt(b.split(':')[0]);
								return hourA - hourB;
							});

							const values = hours.map(hour => parseFloat(hourlyData[hour].toFixed(2)));

							// 更新设备电量统计柱状图数据
							this.devicePowerCategories = hours;
							this.devicePowerData = hours.map((hour, index) => ({
								name: hour,
								value: values[index]
							}));

							// 使用单一的蓝色
							const blueColor = '#2979FF';

							// 更新 uCharts 数据
							this.devicePowerChartData = {
								categories: hours,
								series: [
									{
										name: '用电量',
										data: values,
										color: blueColor // 使用单一的蓝色
									}
								]
							};

							console.log('今日设备电量时间统计数据已更新:', this.devicePowerChartData);
						} else {
							console.log('今日设备电量时间统计数据格式不正确或为空');

							// 如果没有数据，设置为空数组
							this.devicePowerData = [];
							this.devicePowerCategories = [];

							// 更新 uCharts 数据（显示无数据）
							this.devicePowerChartData = {
								categories: [],
								series: [
									{
										name: '用电量',
										data: [],
										color: [] // 空颜色数组
									}
								]
							};
						}
					})
					.catch(err => {
						console.error('获取今日设备电量时间统计数据失败:', err);

						// 如果请求失败，设置为空数组
						this.devicePowerData = [];
						this.devicePowerCategories = [];

						// 更新 uCharts 数据（显示无数据）
						this.devicePowerChartData = {
							categories: [],
							series: [
								{
									name: '用电量',
									data: []
								}
							]
						};
					});
			},

			// 从数据项中提取日期
			extractDate(item) {
				let date;

				if (item.date) {
					// 如果有date字段
					date = new Date(item.date);
				} else if (item.x && typeof item.x === 'string' && item.x.includes('-')) {
					// 如果x字段是日期格式的字符串
					date = new Date(item.x);
				} else if (item.key && typeof item.key === 'string' && item.key.includes('-')) {
					// 如果key字段是日期格式的字符串
					date = new Date(item.key);
				} else if (item.day && typeof item.day === 'number') {
					// 如果有day字段，表示是第几天
					const today = new Date();
					date = new Date(today.getFullYear(), today.getMonth(), today.getDate() - (6 - item.day));
				} else {
					// 如果没有有效的日期字段，使用当前日期
					console.warn('无法从数据中提取日期:', item);
					date = new Date();
				}

				// 确保日期有效
				if (isNaN(date.getTime())) {
					console.warn('提取的日期无效:', item);
					date = new Date(); // 使用当前日期作为后备
				}

				return date;
			},

			// 格式化日期为 "MM-DD" 格式
			formatDate(date) {
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${month}-${day}`;
			},

			// 获取预警/报警数据
			fetchWarningData(groupId) {
				// 构建API请求URL - 参考Vue3版本的getBoxWarningList API
				let url = `/boxWarning?page=0&size=10&sort=createAt&sort=desc`;

				// 只有当groupId存在且有效时，才添加到URL中
				if (groupId) {
					url += `&groupId=${groupId}`;
					console.log('使用groupId构建预警数据URL:', url);
				} else {
					console.log('groupId不存在或无效，不添加到预警数据URL中:', url);
				}

				return this.$http.get(url)
					.then(res => {
						const warningItems = [];
						// 更新预警/报警数据
						if (res && res.content && Array.isArray(res.content)) {
							// 将数据分为预警和报警两类
							const alarmItems = [];
							res.content.forEach(item => {
								const alertItem = {
									time: this.formatDateTime(item.createAt),
									location: item.boxAddress || '未知位置',
									deviceId: item.boxSn || '未知设备',
									message: item.warningTypeName || '未知警告'
								};
								warningItems.push(alertItem);
							});

							this.alarms = warningItems;
						}
					})
					.catch(err => {
						console.error('获取预警/报警数据失败:', err);
					});
			},

			// 切换预警/报警标签页
			switchTab(tabName) {
				this.activeTab = tabName;
			},

			// 格式化日期时间
			formatDateTime(dateTimeStr) {
				if (!dateTimeStr) return '';

				const date = new Date(dateTimeStr);
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');

				return `${hours}:${minutes}`;
			},

			// 更新图表数据 - 参考Vue3版本的数据格式
			updateChartData(data) {
				// 根据API返回的数据更新各个图表
				if (!data) return;

				// 更新设备状态统计数据
				if (data.boxStatusSummary && Array.isArray(data.boxStatusSummary)) {
					// 将数据转换为需要的格式
					const statusData = data.boxStatusSummary.map(item => ({
						name: item.x,
						value: item.y
					}));

					// 计算设备总数
					const totalDevices = statusData.reduce((sum, item) => sum + item.value, 0);

					// 初始化统计数据
					let unused = 0;
					let normal = 0;
					let maintenance = 0;
					let fault = 0;
					let idle = 0;

					// 遍历状态数据
					statusData.forEach(item => {
						if (item.name === '未激活') {
							unused = item.value;
						} else if (item.name === '使用') {
							normal = item.value;
						} else if (item.name === '维修') {
							maintenance = item.value;
						} else if (item.name === '报废') {
							fault = item.value;
						}else if (item.name === '闲置') {
							idle = item.value;
						}
					});

					// 更新数量统计
					this.stats = {
						total: totalDevices || data.totalBoxCheck || 0,
						unused: unused,
						normal: normal,
						maintenance: maintenance,
						fault: fault,
						idle: idle
					};
				} else {
					// 如果没有数据，重置为0
					this.stats = {
						total: 0,
						unused: 0,
						normal: 0,
						maintenance: 0,
						fault: 0,
						idle: 0
					};
				}

				// 同时更新项目能源分布饼图数据
				if (data.boxProjectSummary && Array.isArray(data.boxProjectSummary) && data.boxProjectSummary.length > 0) {
					const projectData = data.boxProjectSummary.map(item => ({
						name: item.x || '未分类',
						value: item.y || 0
					}));

					this.energyDistribution = projectData.map(item => {
						return {
							name: item.name,
							value: item.value,
							color: this.getRandomColor()
						};
					});

					console.log('项目能源分布数据:', this.energyDistribution);
					// 更新项目能源分布饼图
					this.updateDevicePowerChart();
				}else{

					this.energyDistribution = [];
					// 更新项目能源分布饼图（显示无数据）
					this.energyDistributionChartData = {
						series: []
					};
				}


				// 更新设备分布统计（饼图）- 按项目统计
				if (data.boxAddressSummary && Array.isArray(data.boxAddressSummary) && data.boxAddressSummary.length > 0) {
					const projectData = data.boxAddressSummary.map(item => ({
						name: item.x || '未命名项目',
						value: item.y || 0
					}));

					// 创建设备分布数据
					this.deviceDistribution = projectData.map(item => {
						return {
							name: item.name,
							value: item.value,
							color: this.getRandomColor()
						};
					});

					// 更新设备分布饼图
					this.updateDeviceDistributionChart();
				} else {
					// 如果没有数据，设置为空数组
					this.deviceDistribution = [];

					// 更新设备分布饼图（显示无数据）
					this.deviceDistributionChartData = {
						series: []
					};
				}

				// 更新今日巡检进度
				// 使用 totalBoxCheck、todayBoxCheck 和 todayBoxUncheck 字段
				if (data.totalBoxCheck !== undefined || data.todayBoxCheck !== undefined || data.todayBoxUncheck !== undefined) {
					console.log('使用 totalBoxCheck、todayBoxCheck 和 todayBoxUncheck 字段更新巡检数据');

					// 获取总巡检数、今日已巡检和今日未巡检
					const totalBoxCheck = data.totalBoxCheck || 0;
					const todayBoxCheck = data.todayBoxCheck || 0;
					const todayBoxUncheck = data.todayBoxUncheck || 0;

					// 计算总数（今日已巡检 + 今日未巡检）
					const total = todayBoxCheck + todayBoxUncheck;

					console.log('巡检数据:', {
						totalBoxCheck,
						todayBoxCheck,
						todayBoxUncheck,
						total
					});

					this.inspection = {
						total: total, // 总数为今日已巡检 + 今日未巡检
						completed: todayBoxCheck, // 已完成为今日已巡检
						pending: todayBoxUncheck // 未完成为今日未巡检
					};
				} else if (data.inspectionSummary) {
					// 如果没有新字段，则使用旧的 inspectionSummary 字段
					console.log('使用 inspectionSummary 字段更新巡检数据');

					const total = data.inspectionSummary.total || 0;
					const completed = data.inspectionSummary.completed || 0;

					this.inspection = {
						total: total,
						completed: completed,
						pending: total - completed
					};
				} else {
					// 如果没有数据，重置为0
					console.log('没有巡检数据，重置为0');

					this.inspection = {
						total: 0,
						completed: 0,
						pending: 0
					};
				}

				// 注意：今日设备电量时间统计数据会在fetchDeviceEnergyTimeData中更新
				// 这里不再处理deviceEnergySummary数据，避免覆盖从/api/v1/energy/time API获取的数据
				console.log('今日设备电量时间统计数据将从/api/v1/energy/time API获取');

				// 更新用电量数据
				if (data.yearEnergy || data.monthEnergy || data.todayEnergy) {
					this.powerUsage = {
						year: parseFloat(parseFloat(data.yearEnergy || 0).toFixed(2)),
						month: parseFloat(parseFloat(data.monthEnergy || 0).toFixed(2)),
						day: parseFloat(parseFloat(data.todayEnergy || 0).toFixed(2))
					};
				} else {
					// 如果没有数据，重置为0
					this.powerUsage = {
						year: 0,
						month: 0,
						day: 0
					};
				}

				// 更新今日用电量数据
				if (data.energySummaryByProject && Array.isArray(data.energySummaryByProject) && data.energySummaryByProject.length > 0) {
					// 更新数据
					console.log('更新今日用电量数据');

					// 使用单一的蓝色
					const blueColor = '#2979FF';

					// 处理数据
					this.dailyPowerData = data.energySummaryByProject.map(item => ({
						name: item.key === null ? '未分类项目' : (item.key || '未知项目'),
						value: parseFloat(parseFloat(item.value).toFixed(2)) || 0,
						color: blueColor // 强制设置每个柱子为蓝色
					}));

					// 更新类别数据
					this.dailyPowerCategories = this.dailyPowerData.map(item => item.name);

					// 更新图表数据
					this.dailyPowerChartData = {
						categories: this.dailyPowerCategories,
						series: [{
							name: '用电量',
							data: this.dailyPowerData.map(item => item.value),
							color: blueColor // 使用单一的蓝色
						}]
					};

					// 设置Y轴最大值
					const maxValue = this.getMaxValue(this.dailyPowerData);
					this.dailyPowerChartExtra.yAxis.data[0].max = maxValue;

					// 更新项目能源分布数据
					this.energyDistribution = this.dailyPowerData;
					this.updateEnergyDistributionChart();
				} else {
					// 如果没有数据，设置为空数组
					console.log('没有今日用电量数据');
					this.dailyPowerData = [];
					this.dailyPowerCategories = [];
					this.energyDistribution = [];

					// 更新 uCharts 数据（显示无数据）
					this.dailyPowerChartData = {
						categories: [],
						series: [
							{
								name: '用电量',
								data: [],
								color: [] // Empty color array for empty data
							}
						]
					};
				}
			},


			// 生成随机颜色（用于饼图）
			getRandomColor() {
				const colors = ['#4CAF50', '#2196F3', '#FFC107', '#9C27B0', '#F44336', '#009688', '#FF5722', '#673AB7'];
				return colors[Math.floor(Math.random() * colors.length)];
			},

			// 图表点击事件处理
			onChartClick(e) {
				console.log('图表点击事件:', e);
				// 可以在这里处理图表点击事件，例如显示详细信息
			},

			// 图表渲染完成事件处理
			onChartComplete(e) {
				console.log('图表渲染完成:', e);
				// 图表渲染完成后的处理，可以在这里添加额外的逻辑
			},

			// 初始化图表配置
			initChartConfig() {
				console.log('初始化图表配置');

				// 设置图表配置
				this.dailyPowerChartExtra = {
					column: {
						type: 'group', // 使用分组类型
						width: 30,     // 增加柱子宽度
						categoryGap: 30, // 类别间距
						color: '#2979FF' // 使用蓝色
					},
					xAxis: {
						disabled: false,
						disableGrid: false,
						boundaryGap: true,
						axisLine: true,
						calibration: true,
						labelShow: true,
						labelCount: 4, // 与类别数量一致
						itemCount: 4,  // 与类别数量一致
						fontSize: 12,
						margin: 20,
						labelWidth: 80, // 增加标签宽度
						rotateLabel: false, // 禁用标签旋转
						autoSkipLabel: false, // 禁用自动跳过标签
					},
					yAxis: {
						data: [{
							position: 'left',
							axisLine: true,
							axisLineColor: '#CCCCCC',
							calibration: true,
							fontSize: 12,
							disabled: false,
							disableGrid: false,
							labelCount: 3,
							format: (val) => {
								return val.toFixed(0);
							}
						}]
					},
					padding: [15, 15, 80, 15], // 增加底部内边距，为旋转的标签留出空间
					enableScroll: false, // 禁用滚动，确保所有标签都显示
					dataLabel: true
				};

				console.log('图表配置初始化完成');
			},

			// 更新设备分布饼图
			updateDeviceDistributionChart() {
				console.log('开始更新设备分布饼图');

				// 预定义的颜色数组 - 使用固定颜色，确保每次相同类别使用相同颜色
				const colors = ['#4CAF50', '#2196F3', '#FFC107', '#9C27B0', '#F44336', '#009688', '#FF5722', '#673AB7'];

				// 检查 this.deviceDistribution 是否是数组
				if (!this.deviceDistribution || !Array.isArray(this.deviceDistribution) || this.deviceDistribution.length === 0) {
					console.log('设备分布数据不是数组或为空，使用测试数据');

					// 使用默认数据
					this.deviceDistribution = [
						{ name: '正常', value: 42 },
						{ name: '维护中', value: 8 },
						{ name: '故障', value: 3 },
						{ name: '闲置', value: 12 }
					];
				}

				try {
					// 更新 uCharts 数据
					this.deviceDistributionChartData = {
						series: this.deviceDistribution.map((item, index) => ({
							name: item.name,
							data: item.value,
							color: colors[index % colors.length] // 使用固定颜色，避免随机
						}))
					};

					console.log('设备分布饼图数据已更新:', JSON.stringify(this.deviceDistributionChartData));
				} catch (error) {
					console.error('更新设备分布饼图时发生错误:', error);

					// 出错时使用默认数据
					this.deviceDistributionChartData = {
						series: [
							{ name: '正常', data: 42, color: colors[0] },
							{ name: '维护中', data: 8, color: colors[1] },
							{ name: '故障', data: 3, color: colors[2] },
							{ name: '闲置', data: 12, color: colors[3] }
						]
					};

					console.log('使用默认数据更新设备分布饼图');
				}
			},

			// 更新项目能源分布饼图
			updateEnergyDistributionChart() {
				console.log('开始更新项目能源分布饼图');

				// 预定义的颜色数组 - 使用固定颜色，确保每次相同类别使用相同颜色
				const colors = ['#FF5722', '#2196F3', '#FFC107', '#9C27B0', '#4CAF50', '#009688', '#F44336', '#673AB7'];

				// 检查数据是否存在
				if (!this.energyDistribution || !Array.isArray(this.energyDistribution) || this.energyDistribution.length === 0) {
					console.log('项目能源分布数据不是数组或为空，使用测试数据');

					// 使用默认数据
					this.energyDistribution = [
						{ name: '未分类项目', value: 5305.25 },
						{ name: '测试项目新建', value: 301.64 },
						{ name: '天成2', value: 254.42 },
						{ name: '天成项目', value: 2560.96 }
					];
				}

				try {
					// 更新 uCharts 数据
					this.energyDistributionChartData = {
						series: this.energyDistribution.map((item, index) => ({
							name: item.name,
							data: item.value,
							color: colors[index % colors.length] // 使用固定颜色，避免随机
						}))
					};

					console.log('项目能源分布饼图数据已更新:', JSON.stringify(this.energyDistributionChartData));
				} catch (error) {
					console.error('更新项目能源分布饼图时发生错误:', error);

					// 出错时使用默认数据
					this.energyDistributionChartData = {
						series: [
							{ name: '未分类项目', data: 5305.25, color: colors[0] },
							{ name: '测试项目新建', data: 301.64, color: colors[1] },
							{ name: '天成2', data: 254.42, color: colors[2] },
							{ name: '天成项目', data: 2560.96, color: colors[3] }
						]
					};

					console.log('使用默认数据更新项目能源分布饼图');
				}
			},

			// 更新电量走势图表
			updateEnergyTrendChart() {
				this.log('开始更新电量走势图表');

				// 检查数据是否存在
				if (!this.energyTrendData || this.energyTrendData.length === 0) {
					this.log('电量走势数据为空，使用测试数据');

					let testData = [];

					if (this.energyTrendMode === 'DAY') {
						// 按天的测试数据 - 最近7天
						for (let i = 6; i >= 0; i--) {
							const date = new Date();
							date.setDate(date.getDate() - i);
							const month = date.getMonth() + 1;
							const day = date.getDate();
							testData.push({
								time: `${month}/${day}`,
								value: Math.floor(Math.random() * 50) + 100 // 生成100-150之间的随机数
							});
						}
					} else {
						// 按小时的测试数据 - 今天24小时
						for (let i = 0; i < 24; i++) {
							testData.push({
								time: `${i}:00`,
								value: Math.floor(Math.random() * 20) + 10 // 生成10-30之间的随机数
							});
						}
					}

					// 更新数据
					this.energyTrendData = testData;
					this.energyTrendCategories = testData.map(item => item.time);

					// 更新 uCharts 数据
					this.energyTrendChartData = {
						categories: this.energyTrendCategories,
						series: [
							{
								name: '用电量',
								data: testData.map(item => item.value)
							}
						]
					};
				} else {
					console.log('使用现有数据更新电量走势图表');

					// 确保数据格式正确
					const trendData = this.energyTrendData.map(item => {
						return {
							time: item.time,
							value: parseFloat(parseFloat(item.value).toFixed(2))
						};
					});

					// 更新 uCharts 数据
					this.energyTrendChartData = {
						categories: trendData.map(item => item.time),
						series: [
							{
								name: '用电量',
								data: trendData.map(item => item.value)
							}
						]
					};

					// 确保X轴配置正确
					this.energyTrendChartExtra.xAxis = {
						...this.energyTrendChartExtra.xAxis,
						labelCount: 4, // 减少标签数量，只显示4个标签
						itemCount: trendData.length, // 保持数据点数量不变
						labelShow: true, // 强制显示标签
						autoSkipLabel: true, // 启用自动跳过标签
						rotateLabel: true, // 旋转标签
						rotateAngle: 45, // 设置X轴标签旋转角度
						labelWidth: 60 // 增加标签宽度
					};

					// 设置Y轴最大值 - 使用更大的缩放比例
					const maxValue = this.getMaxValue(trendData, 'area');
					this.energyTrendChartExtra.yAxis.data[0].max = maxValue;

					// 确保Y轴只显示3个值：0、最大值的中间值和最大值
					this.energyTrendChartExtra.yAxis.data[0].labelCount = 3;
					this.energyTrendChartExtra.yAxis.data[0].splitNumber = 2;

					// 设置Y轴格式化函数，确保显示小数点后两位
					this.energyTrendChartExtra.yAxis.data[0].format = (val) => {
						return val.toFixed(2);
					};
				}

				this.log('电量走势图表数据已更新:', JSON.stringify(this.energyTrendChartData));
			},

			// 更新设备电量统计柱状图
			updateDevicePowerChart() {
				this.log('开始更新设备电量统计柱状图');

				// 检查数据是否存在
				if (!this.devicePowerData || this.devicePowerData.length === 0) {
					console.log('设备电量统计数据为空，使用测试数据');
				} else {
					console.log('使用现有数据更新设备电量统计柱状图');

					// 确保数据格式正确
					const projectData = this.devicePowerData.map(item => {
						// 如果 item 是对象且有 name 和 value 属性
						if (item && typeof item === 'object' && 'name' in item && 'value' in item) {
							return {
								name: item.name,
								value: parseFloat(parseFloat(item.value).toFixed(2)),
								color: '#2979FF' // 强制设置每个柱子为蓝色
							};
						}
						// 如果 item 是字符串（可能是小时格式）
						else if (typeof item === 'string') {
							const index = this.devicePowerCategories.indexOf(item);
							const value = index >= 0 && this.devicePowerChartData.series[0].data[index]
								? this.devicePowerChartData.series[0].data[index]
								: 0;
							return {
								name: item,
								value: parseFloat(parseFloat(value).toFixed(2)),
								color: '#2979FF' // 强制设置每个柱子为蓝色
							};
						}
						// 默认返回
						return {
							name: '未知',
							value: 0,
							color: '#2979FF' // 强制设置每个柱子为蓝色
						};
					});

					console.log('处理后的项目数据:', projectData);

					// 更新类别数据
					this.devicePowerCategories = projectData.map(item => item.name);

					// 更新 uCharts 数据
					this.devicePowerChartData = {
						categories: this.devicePowerCategories,
						series: [
							{
								name: '用电量',
								data: projectData.map(item => item.value),
								color: '#2979FF' // 使用单一的蓝色
							}
						]
					};

					// 设置Y轴最大值
					const maxValue = this.getMaxValue(projectData);
					this.devicePowerChartExtra.yAxis.data[0].max = maxValue;

					// 强制设置柱状图颜色
					this.devicePowerChartExtra.column = {
						...this.devicePowerChartExtra.column,
						color: '#2979FF'
					};
				}

				console.log('设备电量统计柱状图数据已更新:', JSON.stringify(this.devicePowerChartData));
			},

			// 获取数据中的最大值，用于图表缩放
			getMaxValue(data, chartType = 'default') {
				if (!data || data.length === 0) return 100; // 默认最大值

				// 找出数据中的最大值
				const maxValue = Math.max(...data.map(item => item.value || 0));

				// 根据图表类型使用不同的缩放比例
				if (chartType === 'area' || chartType === 'line') {
					// 折线图和面积图使用更大的缩放比例，增加200%
					return maxValue * 3.0;
				} else {
					// 其他图表类型（如柱状图）使用默认缩放比例，增加60%
					return maxValue * 1.6;
				}
			},

			// 格式化电量数值，保留两位小数
			formatPowerValue(value) {
				if (value === undefined || value === null) {
					return '0.00';
				}

				// 将值转换为数字并保留两位小数
				return parseFloat(value).toFixed(2);
			},

			// 切换标签页
			switchTab(tab) {
				this.activeTab = tab;
			}
		},
		onLoad() {
			// 页面加载时初始化数据
			console.log('首页加载完成');

			try {
				// 检查登录状态
				this.checkLoginStatus();

				// 初始化图表配置
				this.initChartConfig();

				// 如果已登录，从API获取数据
				if (this.isLoggedIn) {
					// 获取今日的开始和结束时间戳
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					const todayStartAt = today.getTime();

					const endToday = new Date();
					endToday.setHours(23, 59, 59, 999);
					const endAt = endToday.getTime();

					// 从用户信息中获取 groupId
					const userInfo = uni.getStorageSync('userInfo');
					const groupId = userInfo?.groupId;

					console.log('获取今日地址电量统计数据, groupId:', groupId);
					this.fetchDeviceEnergyByAddressData(groupId, todayStartAt, endAt);

					console.log('获取今日设备电量统计数据, groupId:', groupId);
					this.fetchDeviceEnergyByBoxIdData(groupId, todayStartAt, endAt);

					// 获取电量走势数据 - 根据当前模式决定时间范围
					let trendStartAt;
					if (this.energyTrendMode === 'DAY') {
						// 如果是按天模式，获取7天前的开始时间戳
						const sevenDaysAgo = new Date();
						sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6); // 减6天，加上今天共7天
						sevenDaysAgo.setHours(0, 0, 0, 0);
						trendStartAt = sevenDaysAgo.getTime();
						console.log('按天模式，获取最近7天数据，开始时间:', new Date(trendStartAt).toLocaleString());
					} else {
						// 如果是按小时模式，使用今日的开始时间戳
						trendStartAt = todayStartAt;
						console.log('按小时模式，获取今日数据，开始时间:', new Date(trendStartAt).toLocaleString());
					}

					console.log('获取电量走势数据, groupId:', groupId);
					this.fetchEnergyTrendData(groupId, trendStartAt, endAt, this.energyTrendMode);

					// 初始化设备分布饼图
					if (typeof this.updateDeviceDistributionChart === 'function') {
						this.updateDeviceDistributionChart();
					} else {
						console.error('updateDeviceDistributionChart 不是一个函数');
					}

					// 初始化项目能源分布饼图
					if (typeof this.updateEnergyDistributionChart === 'function') {
						this.updateEnergyDistributionChart();
					} else {
						console.error('updateEnergyDistributionChart 不是一个函数');
					}
				} else {
					console.log('未登录，不获取API数据');
				}
			} catch (error) {
				console.error('onLoad 钩子中发生错误:', error);
			}
		},
		onShow() {
			// 每次页面显示时检查登录状态
			this.checkLoginStatus();
		}
	}
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative; /* 为了让覆盖层定位正确 */
	}

	/* 登录覆盖层样式 */
	.login-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10; /* 确保覆盖层在最上面 */
		background-color: transparent; /* 透明背景 */
	}





	/* 登录提示区域样式 */
	.login-hint {
		margin-top: 20rpx;
		text-align: center;
	}

	/* 登录按钮样式 */
	.btn-login {
		width: 80%;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 32rpx;
		font-weight: bold;
		background-color: #1976D2;
		color: #fff;
		margin: 0 auto;
		z-index: 20; /* 确保按钮在覆盖层之上 */
		position: relative; /* 为了让z-index生效 */
	}

	/* 更适合一屏展示的登录按钮样式 */
	.btn-login-large {
		width: 80%;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 36rpx;
		font-weight: bold;
		background-color: #1976D2;
		color: #fff;
		margin: 0 auto;
		z-index: 20; /* 确保按钮在覆盖层之上 */
		position: relative; /* 为了让z-index生效 */
		box-shadow: 0 4rpx 10rpx rgba(25, 118, 210, 0.3);
	}

	/* 轮播图样式 */
	.banner-swiper {
		height: 450rpx;
		border-radius: 10rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
	}

	.banner-image {
		width: 100%;
		height: 100%;
	}

	.banner-title {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.5);
		color: #fff;
		padding: 20rpx;
		font-size: 30rpx;
		text-align: center;
	}

	/* 产品介绍样式 */
	.intro-section {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	/* 原始标题样式 */
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
		text-align: center;
		position: relative;
	}

	.section-title:after {
		content: '';
		position: absolute;
		bottom: -10rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #1976D2;
	}

	/* 更适合一屏展示的标题样式 */
	.section-title-large {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		text-align: center;
		position: relative;
	}

	.section-title-large:after {
		content: '';
		position: absolute;
		bottom: -10rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 80rpx;
		height: 4rpx;
		background-color: #1976D2;
	}

	/* 原始网格样式 */
	.intro-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 30rpx;
		margin-bottom: 40rpx;
	}

	/* 更适合一屏展示的网格样式 */
	.intro-grid-large {
		display: grid;
		grid-template-columns: repeat(2, 1fr); /* 改回两列显示，更紧凑 */
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	/* 原始项目样式 */
	.intro-item {
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	/* 更适合一屏展示的项目样式 */
	.intro-item-large {
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 15rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	/* 原始标题样式 */
	.intro-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #1976D2;
		margin-bottom: 10rpx;
	}

	/* 更适合一屏展示的标题样式 */
	.intro-title-large {
		font-size: 32rpx;
		font-weight: bold;
		color: #1976D2;
		margin-bottom: 10rpx;
	}

	/* 原始内容样式 */
	.intro-content {
		font-size: 24rpx;
		color: #666;
		line-height: 1.5;
	}

	/* 更适合一屏展示的内容样式 */
	.intro-content-large {
		font-size: 28rpx;
		color: #333;
		line-height: 1.4;
	}



	/* 租户选择器样式 */
	.tenant-selector {
		background-color: #1976D2;
		color: #fff;
		padding: 20rpx 30rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.selector-title {
		font-size: 28rpx;
	}

	.picker-content {
		background-color: rgba(255, 255, 255, 0.2);
		padding: 10rpx 20rpx;
		border-radius: 6rpx;
		display: flex;
		align-items: center;
	}

	.arrow-down {
		margin-left: 10rpx;
		font-size: 24rpx;
	}

	/* 统计卡片网格 */
	.stats-grid {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.stats-card {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.card-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #eee;
		padding-bottom: 10rpx;
	}

	.card-content {
		padding: 10rpx 0;
		width: 100%;
		box-sizing: border-box;
	}

	/* 数量统计样式 */
	.stats-content {
		display: flex;
		justify-content: space-around;
		flex-wrap: wrap;
	}

	.stats-item {
		display: inline-flex;
		flex-direction: column;
		align-items: center;
		width: 16.66%; /* 设置宽度为16.66%，因为有6个项目 */
		margin-bottom: 10rpx;
		box-sizing: border-box;
	}

	/* 总用电量卡片样式 - 垂直布局 */
	.power-usage-content-vertical {
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		padding: 20rpx 0;
	}

	.power-usage-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx 30rpx;
		margin: 5rpx 0;
	}

	.power-usage-label {
		font-size: 28rpx;
		color: #666;
		font-weight: bold;
	}

	.power-usage-value {
		font-size: 28rpx;
		color: #1976D2;
		font-weight: bold;
	}

	.stats-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.stats-label {
		font-size: 24rpx;
		color: #666;
		margin-top: 5rpx;
	}

	/* 报警列表样式 */
	.alert-tabs {
		display: flex;
		border-bottom: 1rpx solid #eee;
		margin-bottom: 10rpx;
	}

	.alert-tab {
		flex: 1;
		text-align: center;
		padding: 15rpx 0;
		font-size: 28rpx;
		color: #666;
		position: relative;
	}

	.alert-tab.active {
		color: #1976D2;
		font-weight: bold;
	}

	.alert-tab.active:after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 25%;
		width: 50%;
		height: 4rpx;
		background-color: #1976D2;
	}

	.alert-list {
		height: 250rpx; /* 设置固定高度，留出表头的空间 */
		overflow-y: auto; /* 内容超出时显示滚动条 */
		border-bottom: 1rpx solid #e0e0e0;
	}

	/* 表格样式 */
	.alert-table {
		width: 100%;
		border-collapse: collapse;
		font-size: 24rpx;
	}

	.alert-table-header {
		display: flex;
		background-color: #f5f5f5;
		border-bottom: 1rpx solid #e0e0e0;
		position: sticky; /* 使表头固定 */
		top: 0; /* 固定在顶部 */
		z-index: 10; /* 确保表头在内容之上 */
		font-size: 14px;
		text-align: left;
	}

	.alert-table-row {
		display: flex;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.alert-table-cell {
		flex: 1;
		padding: 15rpx 10rpx;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.alert-time {
		color: #f44336;
	}

	.alert-location, .alert-device, .alert-message {
		color: #333;
	}

	.no-data {
		text-align: center;
		color: #999;
		padding: 30rpx 0;
	}

	/* 图表卡片样式 */
	.chart-card {
		min-height: 250rpx;
		max-height: 700px;
	}

	.chart-container {
		height: 350rpx;
		width: 100%;
		position: relative;
	}

	.chart-placeholder {
		height: 300rpx;
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.chart-note {
		text-align: center;
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
	}

	/* 图表容器样式 */
	.chart-wrapper {
		width: 100%;
		height: 400rpx;
		position: relative;
		margin-top: 20rpx;
	}

	.chart-container {
		width: 100%;
		height: 100%;
		position: relative;
	}

	/* 调试信息样式 */
	.debug-info {
		padding: 10rpx;
		background-color: #f8f8f8;
		border-radius: 5rpx;
		margin-bottom: 10rpx;
		font-size: 24rpx;
		color: #666;
	}

	.debug-data {
		padding: 10rpx;
		background-color: #f8f8f8;
		border-radius: 5rpx;
		margin-top: 10rpx;
		font-size: 24rpx;
		color: #666;
		max-height: 200rpx;
		overflow-y: auto;
	}

	.debug-item {
		padding: 5rpx 0;
		border-bottom: 1px solid #eee;
	}

	/* 图表轴样式 */
	.chart-axis {
		display: flex;
		width: 100%;
		height: 250rpx;
	}

	.y-axis {
		width: 60rpx;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		border-right: 1rpx solid #eee;
	}

	.x-axis {
		width: 100%;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-top: 1rpx solid #eee;
		margin-top: 10rpx;
	}

	.axis-label {
		font-size: 24rpx;
		color: #666;
		writing-mode: vertical-lr;
		transform: rotate(180deg);
	}

	.x-axis .axis-label {
		writing-mode: horizontal-tb;
		transform: none;
	}

	/* 图表区域样式 */
	.chart-area {
		flex: 1;
		height: 100%;
		padding: 10rpx;
	}

	/* 柱状图样式 */
	.bar-chart {
		display: flex;
		justify-content: space-around;
		align-items: flex-end;
		height: 100%;
		width: 100%;
	}

	.bar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		height: 100%;
	}

	.bar {
		width: 40rpx;
		border-radius: 4rpx 4rpx 0 0;
		display: flex;
		justify-content: center;
		align-items: flex-start;
		position: relative;
	}

	.bar-value {
		position: absolute;
		top: -30rpx;
		font-size: 20rpx;
		color: #333;
	}

	.bar-label {
		font-size: 20rpx;
		color: #666;
		margin-top: 10rpx;
		text-align: center;
		width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 折线图样式 */
	.line-chart {
		position: relative;
		width: 100%;
		height: 100%;
	}

	/* 网格线样式 */
	.grid-lines {
		position: absolute;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	.grid-line {
		position: absolute;
		width: 100%;
		height: 1rpx;
		background-color: #f0f0f0;
	}

	/* 折线样式 */
	.line-path {
		position: absolute;
		width: 100%;
		height: 100%;
		z-index: 2;
	}

	.line-segment {
		position: absolute;
		background-color: #4CAF50;
		z-index: 2;
	}

	/* 数据点样式 */
	.line-point {
		position: absolute;
		width: 12rpx;
		height: 12rpx;
		background-color: #4CAF50;
		border-radius: 50%;
		transform: translate(-50%, 50%);
		z-index: 3;
	}

	.point-value {
		position: absolute;
		top: -30rpx;
		left: 50%;
		transform: translateX(-50%);
		font-size: 20rpx;
		color: #333;
		white-space: nowrap;
	}

	/* X轴标签样式 */
	.line-labels {
		position: absolute;
		bottom: -30rpx;
		width: 100%;
		height: 30rpx;
		z-index: 1;
	}

	.line-label {
		position: absolute;
		transform: translateX(-50%);
		font-size: 20rpx;
		color: #666;
		text-align: center;
	}

	/* 柱状图样式 */
	.chart-column-container {
		position: absolute;
		left: 60rpx;
		top: 0;
		right: 0;
		bottom: 40rpx;
		display: flex;
		justify-content: space-around;
		align-items: flex-end;
		padding: 0 20rpx;
	}

	.chart-column-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 18%;
	}

	.chart-column {
		width: 40rpx;
		background-color: #2196F3;
		border-radius: 4rpx 4rpx 0 0;
		transition: height 0.3s ease;
	}

	.chart-label {
		font-size: 20rpx;
		color: #666;
		margin-top: 10rpx;
		text-align: center;
		width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 折线图样式 */
	.chart-line-container {
		position: absolute;
		left: 60rpx;
		top: 0;
		right: 0;
		bottom: 40rpx;
		padding: 0 20rpx;
	}

	.chart-line-point {
		width: 12rpx;
		height: 12rpx;
		background-color: #4CAF50;
		border-radius: 50%;
		position: absolute;
		transform: translate(-50%, 50%);
		z-index: 2;
	}

	.chart-line-path {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	.chart-line-labels {
		position: absolute;
		left: 60rpx;
		right: 0;
		bottom: 0;
		height: 40rpx;
		display: flex;
		justify-content: space-around;
		padding: 0 20rpx;
	}

	.chart-line-label {
		position: absolute;
		font-size: 20rpx;
		color: #666;
		transform: translateX(-50%);
		text-align: center;
		width: 60rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 进度条样式 */
	.inspection-stats {
		display: flex;
		justify-content: space-around;
		margin-bottom: 20rpx;
		width: 100%;
	}

	.inspection-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 33.33%; /* 设置宽度为33.33%，因为有3个项目 */
		box-sizing: border-box;
	}

	.inspection-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.inspection-label {
		font-size: 24rpx;
		color: #666;
	}

	.progress-bar {
		height: 20rpx;
		background-color: #f0f0f0;
		border-radius: 10rpx;
		overflow: hidden;
		margin: 10rpx 0;
	}

	.progress-inner {
		height: 100%;
		background-color: #4CAF50;
		border-radius: 10rpx;
	}

	.progress-text {
		font-size: 24rpx;
		color: #666;
		text-align: center;
	}

	/* 饼图样式 */
	.pie-chart {
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		position: relative;
		overflow: hidden;
	}

	.pie-segment {
		position: absolute;
		width: 100%;
		height: 100%;
		transform-origin: 50% 50%;
		background: conic-gradient(var(--segment-color) var(--segment-start), var(--segment-color) var(--segment-end), transparent var(--segment-end));
	}

	.pie-center {
		position: absolute;
		width: 60%;
		height: 60%;
		background-color: white;
		border-radius: 50%;
		top: 20%;
		left: 20%;
	}

	.pie-legend {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		margin-top: 20rpx;
	}

	.legend-item {
		display: flex;
		align-items: center;
		margin: 0 15rpx 10rpx 0;
	}

	.legend-color {
		width: 20rpx;
		height: 20rpx;
		border-radius: 4rpx;
		margin-right: 8rpx;
	}

	.legend-text {
		font-size: 22rpx;
		color: #666;
	}

	/* 电量走势模式切换按钮样式 */
	.mode-btn {
		background-color: #f0f0f0;
		color: #666;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.mode-btn.active-mode {
		background-color: #007AFF;
		color: #fff;
	}

	/* 设备电能排行列表样式 */
	.energy-ranking-list {
		width: 100%;
	}

	.energy-ranking-header {
		display: flex;
		background-color: #f5f5f5;
		padding: 15rpx 0;
		font-weight: bold;
		border-bottom: 1rpx solid #eee;
		font-size: 28rpx; /* 与 card-title 一致 */
		color: #333; /* 与 card-title 一致 */
	}

	.energy-ranking-item {
		display: flex;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.energy-ranking-cell {
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-size: 28rpx; /* 与 card-title 一致 */
		color: #333; /* 与 card-title 一致 */
	}

	.rank-cell {
		width: 20%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.device-cell {
		width: 50%;
		text-align: left;
		padding-left: 20rpx;
	}

	.energy-cell {
		width: 30%;
		font-weight: bold; /* 强调电量值 */
		color: #1976D2; /* 使用蓝色突出显示 */
	}

	.rank-badge {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-weight: bold;
		font-size: 28rpx; /* 与 card-title 一致 */
	}

	.rank-1 {
		background-color: #FFD700; /* 金色 */
	}

	.rank-2 {
		background-color: #C0C0C0; /* 银色 */
	}

	.rank-3 {
		background-color: #CD7F32; /* 铜色 */
	}

	.no-data-tip {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 28rpx; /* 与 card-title 一致 */
	}
</style>
