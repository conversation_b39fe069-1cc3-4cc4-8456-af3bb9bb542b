<template>
	<view class="content">
		<view class="header">
			<text class="title"></text>
			<view class="header-buttons">
				<button v-if="isAdmin" class="btn-action" :class="{'btn-disable': checkable, 'btn-enable': !checkable}" @click="toggleInspection">
					{{ checkable ? '关闭点检' : '开启点检' }}
				</button>
				<button v-if="isInspector && checkable" class="btn-action btn-inspect" @click="startInspection">开始点检</button>
				<button class="btn-action btn-subscribe" @click="handleSubscribe">订阅通知</button>
				<button class="btn-action btn-logout" @click="logout">退出登录</button>
			</view>
		</view>

		<view class="filter-bar">
			<text class="filter-label">历史点检记录</text>
			<view class="filter-options">
				<text class="filter-option" :class="{ active: timeFilter === 'today' }" @click="setTimeFilter('today')">今天</text>
				<text class="filter-option" :class="{ active: timeFilter === 'week' }" @click="setTimeFilter('week')">本周</text>
				<text class="filter-option" :class="{ active: timeFilter === 'month' }" @click="setTimeFilter('month')">本月</text>
				<text class="filter-option" :class="{ active: timeFilter === 'all' }" @click="setTimeFilter('all')">全部</text>
			</view>
		</view>

		<view class="inspection-list">
			<view class="empty-tip" v-if="filteredInspections.length === 0">
				<text>暂无点检记录</text>
			</view>
			<view class="inspection-card" v-for="(inspection, index) in filteredInspections" :key="index" @click="viewInspectionDetail(inspection)">
				<!-- 标题行：左侧显示时间，右侧显示状态 -->
				<view class="inspection-header">
					<text class="inspection-title">{{formatDate(inspection.inspectionTime)}}</text>
					<text class="inspection-status" :class="{
						'status-normal': inspection.status === 'normal',
						'status-warning': inspection.status === 'warning',
						'status-danger': inspection.status === 'danger'
					}">
						{{getStatusText(inspection.status)}}
					</text>
				</view>

				<!-- 信息区域：采用表格式布局，确保标签和值对齐 -->
				<view class="inspection-info">
					<!-- 设备ID信息 -->
					<view class="info-row" v-if="inspection.sn">
						<text class="info-label">设备ID:</text>
						<text class="info-value">{{inspection.sn}}</text>
					</view>
					<!-- 位置信息 -->
					<view class="info-row" v-if="inspection.location">
						<text class="info-label">位置:</text>
						<text class="info-value">{{inspection.location}}</text>
					</view>
					<!-- 点检人信息 -->
					<view class="info-row">
						<text class="info-label">点检人:</text>
						<text class="info-value">{{inspection.inspector}}</text>
					</view>
				</view>

				<!-- 备注信息（如果有） -->
				<view class="inspection-footer" v-if="inspection.remark || inspection.attachment">
					<text class="remark-label">备注:</text>
					<text class="inspection-remark">{{inspection.remark || inspection.attachment}}</text>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="load-more" v-if="inspections.length > 0">
				<text v-if="hasMoreData && !isLoading">上拉加载更多</text>
				<text v-if="isLoading">正在加载...</text>
				<text v-if="!hasMoreData">没有更多数据了</text>
			</view>
			<view class="refresh-tip">
				<text>下拉页面可刷新数据</text>
			</view>
		</view>
	</view>
</template>

<script>
	// 消息订阅模板ID
	const SUBSCRIBE_TEMPLATE_ID = 'NvSnWo3_QukkvtCZYnP7LUJkCogj_Ylr8IaCVJHP-1s';

	export default {
		data() {
			return {
				title: '点检',
				timeFilter: 'today', // 时间筛选：today, week, month, all
				isInspector: false, // 用户是否是点检员
				isAdmin: false, // 用户是否是管理员
				checkable: false, // 是否可以点检
				newCheckableValue: false, // 用于在Promise链中传递新的checkable值
				hasShownSubscribeDialog: false, // 标记当前会话是否已经显示过订阅对话框
				groupId: '', // 用户所属组ID
				currentPage: 0, // 当前页码
				hasMoreData: true, // 是否还有更多数据
				isLoading: false, // 是否正在加载数据
				inspections: [], // 点检记录列表
				subscribeTemplateId: SUBSCRIBE_TEMPLATE_ID, // 订阅消息模板ID
				hasRequestedSubscription: false // 是否已经请求过订阅
			}
		},
		computed: {
			// 根据时间筛选过滤点检记录
			filteredInspections() {
				if (this.timeFilter === 'all') {
					return this.inspections;
				}

				const now = new Date();
				const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
				const weekStart = new Date(today);
				weekStart.setDate(today.getDate() - today.getDay());
				const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

				return this.inspections.filter(inspection => {
					const inspectionDate = new Date(inspection.inspectionTime);

					if (this.timeFilter === 'today') {
						return inspectionDate >= today;
					} else if (this.timeFilter === 'week') {
						return inspectionDate >= weekStart;
					} else if (this.timeFilter === 'month') {
						return inspectionDate >= monthStart;
					}

					return true;
				});
			}
		},
		onLoad() {
			// 页面加载时检查登录状态
			console.log('页面加载(onLoad)，准备检查登录状态');
			this.checkLoginStatus(true); // 传入true表示是首次加载，需要获取点检状态
		},
		onShow() {
			// 每次页面显示时都检查登录状态，并获取最新的点检状态
			console.log('页面显示(onShow)，准备检查登录状态');
			this.checkLoginStatus(true); // 传入true表示需要获取点检状态

			// 如果已登录，则获取点检列表数据
			if (uni.getStorageSync('token') && uni.getStorageSync('isLoggedIn')) {
				console.log('用户已登录，准备刷新数据');
				this.refreshData();
			}
		},
		// 页面触底事件
		onReachBottom() {
			console.log('页面触底，加载更多数据');
			this.loadMoreData();
		},
		// 下拉刷新事件
		onPullDownRefresh() {
			console.log('下拉刷新');
			// 调用刷新数据方法
			this.refreshData();

			// 停止下拉刷新动画
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		methods: {
			// 处理订阅按钮点击
			handleSubscribe() {
				console.log('用户点击订阅按钮');

				// 检查环境是否支持订阅消息
				if (!wx.requestSubscribeMessage) {
					console.log('当前环境不支持订阅消息');
					uni.showToast({
						title: '当前环境不支持订阅消息',
						icon: 'none',
						duration: 1500
					});
					return;
				}

				// 请求用户订阅消息
				this.requestSubscribeMessage();
			},

			// 检查登录状态
			checkLoginStatus(needGetStatus = true) {
				console.log('开始检查登录状态...', needGetStatus ? '需要获取点检状态' : '不需要获取点检状态');

				// 从存储中获取token和过期时间
				const token = uni.getStorageSync('token');
				const tokenExpireAt = uni.getStorageSync('tokenExpireAt');
				const isLoggedIn = uni.getStorageSync('isLoggedIn');

				console.log('登录信息检查 - token:', token ? '存在' : '不存在', 'isLoggedIn:', isLoggedIn);

				// 检查token是否存在
				if (!token || !isLoggedIn) {
					console.log('未登录，准备重定向到登录页面');
					this.redirectToLogin('请先登录');
					return;
				}

				console.log('用户已登录，继续检查角色信息');

				// 获取用户角色信息
				this.isInspector = uni.getStorageSync('isInspector') || false;
				this.isAdmin = uni.getStorageSync('isGroupAdmin') || false; // 使用isGroupAdmin而不是isAdmin

				// 调试日志
				console.log('登录状态检查 - isGroupAdmin:', this.isAdmin, 'isInspector:', this.isInspector);

				// 获取用户信息
				const userInfo = uni.getStorageSync('userInfo');

				// 确保角色设置正确
				if (userInfo && userInfo.admin) {
					console.log('检测到管理员用户，设置isAdmin=true');
					this.isAdmin = true;
					uni.setStorageSync('isGroupAdmin', true); // 使用isGroupAdmin而不是isAdmin
				}

				// 获取用户组ID
				if (userInfo && userInfo.groupId) {
					this.groupId = userInfo.groupId;
				}

				// 只有在需要时才获取点检状态
				if (needGetStatus) {
					console.log('需要获取点检状态，调用getInspectionStatus方法');
					// 无论是否有groupId，都尝试获取点检状态
					// 因为/me/group API会返回当前用户的组信息
					this.getInspectionStatus();
				}

				// 查询报警通知设置
				this.checkWarningNotifyConfig();

				// 检查token是否过期
				if (tokenExpireAt) {
					const expireDate = new Date(tokenExpireAt);
					const now = new Date();

					if (now >= expireDate) {
						// 清除过期的token、登录状态和用户信息
						uni.removeStorageSync('token');
						uni.removeStorageSync('tokenExpireAt');
						uni.removeStorageSync('isLoggedIn');
						uni.removeStorageSync('userInfo');
						uni.removeStorageSync('userAdmin');
						uni.removeStorageSync('userGroupAdmin');
						uni.removeStorageSync('userGroupId');
						uni.removeStorageSync('userGroupName');
						uni.removeStorageSync('userId');
						uni.removeStorageSync('userName');
						uni.removeStorageSync('userRealName');

						// 清除角色相关信息
						uni.removeStorageSync('userRoleIdList');
						uni.removeStorageSync('userRoleTypes');
						uni.removeStorageSync('isAdmin');
						uni.removeStorageSync('isOperator');
						uni.removeStorageSync('isGroupAdmin');
						uni.removeStorageSync('isGroupOperator');
						uni.removeStorageSync('isInspector');
						uni.removeStorageSync('isVisitor');

						this.redirectToLogin('登录已过期，请重新登录');
						return;
					}
				}
			},

			// 重定向到登录页面
			redirectToLogin(message) {
				uni.showToast({
					title: message || '请先登录',
					icon: 'none',
					duration: 1500
				});

				// 延迟跳转，让用户看到提示
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					});
				}, 1500);
			},

			// 设置时间筛选
			setTimeFilter(filter) {
				this.timeFilter = filter;
			},

			// 将API返回的状态映射到我们的状态格式
			mapApiStatus(apiStatus) {
				// 根据实际API返回的状态值进行映射
				// API返回的点检结果：1-正常，2-异常，3-超出范围

				// 首先将 apiStatus 转换为数字，以防它是字符串
				const statusNum = Number(apiStatus);

				switch (statusNum) {
					case 1:
						return 'normal'; // 1-正常
					case 2:
						return 'danger'; // 2-异常
					case 3:
						return 'warning'; // 3-超出范围
					default:
						console.log('未知API状态值:', apiStatus, '类型:', typeof apiStatus);
						return 'normal'; // 默认为正常
				}
			},

			// 获取状态样式类
			getStatusClass(status) {
				switch (status) {
					case 'normal':
						return 'status-normal';
					case 'warning':
						return 'status-warning';
					case 'danger':
						return 'status-danger';
					default:
						return '';
				}
			},

			// 获取状态文本
			getStatusText(status) {
				// 首先将 status 转换为字符串，以防它是数字
				const statusStr = String(status);

				// 处理数字类型的 status 值（API返回的点检结果：1-正常，2-异常，3-超出范围）
				if (statusStr === '1') {
					return '正常';
				} else if (statusStr === '2') {
					return '异常'; // 确保 checkResult 为 2 时返回"异常"
				} else if (statusStr === '3') {
					return '超出范围';
				}

				// 处理字符串类型的 status 值
				switch (statusStr) {
					case 'normal':
						return '正常';
					case 'warning':
						return '超出范围'; // 修改为与数字类型一致
					case 'danger':
						return '异常'; // 修改为与数字类型一致
					default:
						// 如果 status 是其他值，打印出来以便调试
						console.log('未知状态值:', status, '类型:', typeof status);
						return '未知';
				}
			},

			// 格式化日期
			formatDate(dateString) {
				const date = new Date(dateString);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');

				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},

			// 查看点检详情
			viewInspectionDetail(inspection) {
				console.log('查看点检详情:', inspection.id);
				uni.showToast({
					title: `查看点检: ${inspection.location}`,
					icon: 'none'
				});
				// 这里可以跳转到点检详情页
				// uni.navigateTo({
				//   url: `/pages/inspection/detail?id=${inspection.id}`
				// });
			},

			// 获取点检状态
			getInspectionStatus() {
				console.log('开始获取点检状态...');
				// 不再检查groupId，因为/me/group API会返回当前用户的组信息

				// 显示加载中
				uni.showLoading({
					title: '获取状态中...'
				});

				// 直接调用API获取点检状态，不使用调试模式

				// 调用API获取点检状态
				console.log('准备调用API获取点检状态:', `/me/group`);

				// 获取token
				const token = uni.getStorageSync('token');
				console.log('当前token:', token ? token.substring(0, 10) + '...' : '不存在');

				this.$http.get(`/me/group`)
					.then(res => {
						uni.hideLoading();
						console.log('获取点检状态成功，API响应:', res);

						// 更新点检状态
						if (res && res.checkable !== undefined) {
							console.log('API返回的checkable值:', res.checkable);
							console.log('API返回的checkable类型:', typeof res.checkable);

							// 确保checkable是布尔值
							if (typeof res.checkable === 'string') {
								// 如果是字符串，转换为布尔值
								this.checkable = res.checkable.toLowerCase() === 'true';
								console.log('将字符串转换为布尔值:', this.checkable);
							} else {
								// 否则直接赋值
								this.checkable = res.checkable;
							}

							console.log('更新本地checkable状态为:', this.checkable);
							console.log('更新后的checkable类型:', typeof this.checkable);
						} else {
							console.log('API响应中没有checkable字段，完整响应:', JSON.stringify(res));
						}

						// 如果没有groupId，从API响应中获取
						if (!this.groupId && res && res.id) {
							this.groupId = res.id;
							console.log('从API响应中获取groupId:', this.groupId);

							// 保存到本地存储
							const userInfo = uni.getStorageSync('userInfo') || {};
							userInfo.groupId = this.groupId;
							uni.setStorageSync('userInfo', userInfo);
							console.log('已将groupId保存到本地存储');
						}
					})
					.catch(err => {
						uni.hideLoading();
						console.error('获取点检状态失败，错误信息:', err);

						// 显示详细错误信息
						if (err.response) {
							console.error('错误响应状态码:', err.response.status);
							console.error('错误响应数据:', err.response.data);
						}

						if (err.request) {
							console.error('请求配置:', err.request);
						}

						console.error('错误消息:', err.message);
						console.error('错误堆栈:', err.stack);

						// 出错时设置为可点检
						console.log('API调用失败，设置默认点检状态');
						this.checkable = true;

						// 显示错误提示
						uni.showToast({
							title: '获取点检状态失败，使用默认设置',
							icon: 'none'
						});

						// 显示详细错误对话框
						uni.showModal({
							title: 'API调用失败',
							content: '获取点检状态失败，错误信息: ' + JSON.stringify(err),
							showCancel: false
						});
					});
			},

			// 切换点检状态
			toggleInspection() {
				if (!this.groupId) {
					console.error('切换点检状态失败: 缺少groupId');
					uni.showToast({
						title: '操作失败: 缺少组ID',
						icon: 'none'
					});
					return;
				}

				// 记录当前状态
				console.log('toggleInspection - 当前点检状态:', this.checkable);

				// 显示确认对话框，提示用户操作的影响
				const willEnable = !this.checkable;
				console.log('toggleInspection - 将要设置的点检状态:', willEnable);

				const confirmTitle = willEnable ? '开启点检' : '关闭点检';
				const confirmContent = willEnable ?
					'确定要开启点检吗？' :
					'确定要关闭点检吗？';

				uni.showModal({
					title: confirmTitle,
					content: confirmContent,
					success: (res) => {
						if (res.confirm) {
							// 用户点击确定，继续操作
							this.executeToggleInspection();
						}
						// 用户点击取消，不执行任何操作
					}
				});
			},

			// 执行切换点检状态
			executeToggleInspection() {
				// 显示加载中
				uni.showLoading({
					title: '处理中...'
				});


				// 先获取当前组信息
				console.log('先获取当前组信息');
				this.$http.get(`/me/group`)
					.then(groupInfo => {
						console.log('获取组信息成功:', groupInfo);

						// 如果没有groupId，从API响应中获取
						if (!this.groupId && groupInfo && groupInfo.id) {
							this.groupId = groupInfo.id;
							console.log('从API响应中获取groupId:', this.groupId);
						}

						// 确保有groupId
						if (!this.groupId) {
							throw new Error('无法获取组ID，无法更新点检状态');
						}

						// 记录当前状态
						console.log('当前点检状态(this.checkable):', this.checkable);
						console.log('当前点检状态类型:', typeof this.checkable);

						// 确保this.checkable是布尔值
						let currentCheckable = this.checkable;
						if (typeof currentCheckable === 'string') {
							currentCheckable = currentCheckable.toLowerCase() === 'true';
							console.log('将字符串转换为布尔值:', currentCheckable);
						}

						// 计算新的checkable值
						const newCheckable = !currentCheckable;
						console.log('将要设置的点检状态:', newCheckable);

						// 保留原有字段，只修改checkable
						const requestData = {
							...groupInfo,  // 保留所有原有字段
							checkable: newCheckable  // 只修改checkable字段
						};

						console.log('发送PUT请求到:', `/groups/${this.groupId}`);
						console.log('完整请求数据:', JSON.stringify(requestData));
						console.log('请求数据中的checkable值:', requestData.checkable);

						// 保存新的checkable值到Vue实例，以便在下一个then中使用
						this.newCheckableValue = newCheckable;

						// 调用API更新点检状态
						return this.$http.put(`/groups/${this.groupId}`, requestData);
					})
					.then(res => {
						uni.hideLoading();
						console.log('切换点检状态成功:', res);

						// 更新本地点检状态
						// 使用之前保存的newCheckableValue值
						this.checkable = this.newCheckableValue;
						console.log('API调用成功后，更新本地点检状态为:', this.checkable);

						// 显示成功提示和详细信息
						const successTitle = this.checkable ? '已开启点检' : '已关闭点检';
						console.log('成功提示标题:', successTitle);
						// 先显示简短提示
						uni.showToast({
							title: successTitle,
							icon: 'success',
							duration: 1500
						});

					})
					.catch(err => {
						uni.hideLoading();
						console.error('操作失败:', err);

						// 显示错误信息
						console.log('API调用失败，显示错误信息');

						// 显示错误提示
						uni.showToast({
							title: '操作失败，请重试',
							icon: 'none'
						});

						// 显示详细错误对话框
						uni.showModal({
							title: '调用API失败',
							content: '请检查网络连接和API服务是否正常。错误信息: ' + JSON.stringify(err),
							showCancel: false
						});
					});
			},

			// 开始新的点检
			startInspection() {
				console.log('开始新的点检');

				// 获取用户位置信息
				this.getUserLocation().then(location => {
					// 位置获取成功后，调用扫码功能
					this.scanQRCode(location);
				}).catch(err => {
					// 位置获取失败，仍然可以继续扫码
					console.error('获取位置失败:', err);
					uni.showToast({
						title: '无法获取位置信息，但您仍可继续点检',
						icon: 'none',
						duration: 2000
					});

					// 延迟一下再调用扫码，让用户看到提示
					setTimeout(() => {
						this.scanQRCode();
					}, 2000);
				});
			},

			// 获取用户位置
			getUserLocation() {
				return new Promise((resolve, reject) => {
					uni.getLocation({
						type: 'gcj02', // 使用国测局坐标系
						success: (res) => {
							console.log('获取位置成功:', res);
							// 返回经纬度信息
							resolve({
								latitude: res.latitude,
								longitude: res.longitude
							});
						},
						fail: (err) => {
							console.error('获取位置失败:', err);
							// 检查是否是权限问题
							if (err.errMsg.includes('auth deny')) {
								uni.showModal({
									title: '提示',
									content: '需要获取您的位置信息才能进行点检，请在设置中允许访问位置信息',
									confirmText: '去设置',
									cancelText: '取消',
									success: (res) => {
										if (res.confirm) {
											// 打开设置页面
											uni.openSetting({
												success: (settingRes) => {
													console.log('设置页面返回:', settingRes);
												}
											});
										}
									}
								});
							}
							reject(err);
						}
					});
				});
			},

			// 扫描二维码
			scanQRCode(location) {
				uni.scanCode({
					onlyFromCamera: true, // 只允许从相机扫码
					scanType: ['qrCode'], // 只扫描二维码
					success: (res) => {
						console.log('扫码成功:', res);

						// 处理扫码结果
						const qrResult = res.result;

						// 显示扫码结果
						uni.showLoading({
							title: '正在处理...'
						});

						// 获取token
						const token = uni.getStorageSync('token');
						if (!token) {
							uni.hideLoading();
							uni.showToast({
								title: '请先登录',
								icon: 'none'
							});
							return;
						}

						// 使用扫描到的ID调用API获取设备信息
						const qrcode = qrResult;

						// 对qrcode进行URL编码，处理可能包含的特殊字符
						const encodedQrcode = encodeURIComponent(qrcode);
						console.log('原始qrcode:', qrcode);
						console.log('编码后的qrcode:', encodedQrcode);

						// 构建API请求URL
						const url = `/distributionBox?qrcode=${encodedQrcode}&page=0&size=10`;

						// 调用API获取设备信息
						this.$http.get(url)
							.then(res => {
								uni.hideLoading();
								console.log('获取设备信息成功:', res);

								// 如果返回的是分页对象，提取第一个元素
								if (res && res.content && Array.isArray(res.content) && res.content.length== 0) {
									// 显示提示
									uni.showToast({
										title: '获得配电箱信息失败',
										icon: 'error',
										duration: 1500
									});

									return;
								}

								// 准备点检数据
								const inspectionData = {
									deviceId: res.id, // 使用扫描到的ID
									deviceName: res.name ,
									projectId: res.projectId,
									projectName: res.projectName,
									boxAddressId: res.boxAddressId,
									boxAddress: res.boxAddress,
									status: 'success',
									location: location, // 点检员手机的位置
									boxInfo: res, // 保存完整的设备信息
									qrcode: qrcode, // 保存二维码信息
									remark: '点检正常，设备运行良好'
								};

								// 将点检数据转为URL参数
								const dataParam = encodeURIComponent(JSON.stringify(inspectionData));
								// 跳转到点检结果页面
								uni.navigateTo({
									url: `/pages/inspection/result?data=${dataParam}`
								});

								// 刷新点检列表
								this.refreshData();
							})
							.catch(err => {
								uni.hideLoading();
								console.error('获取设备信息失败:', err);
							});
					},
					fail: (err) => {
						console.error('扫码失败:', err);
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'none'
						});
					}
				});
			},

			// 退出登录
			logout() {
				// 显示确认对话框
				uni.showModal({
					title: '退出登录',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 用户点击确定，清除所有storage
							this.clearAllStorage();

							// 显示提示
							uni.showToast({
								title: '已退出登录',
								icon: 'success',
								duration: 1500
							});

							// 延迟跳转到登录页面
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}, 1500);
						}
					}
				});
			},

			// 请求用户订阅消息
			requestSubscribeMessage() {
				console.log('请求用户订阅消息...');

				// 请求用户订阅消息
				wx.requestSubscribeMessage({
					tmplIds: [this.subscribeTemplateId],
					success: (res) => {
						console.log('订阅消息结果:', res);

						// 根据订阅结果处理
						if (res[this.subscribeTemplateId] === 'accept') {
							console.log('用户接受订阅消息');
							uni.showToast({
								title: '订阅成功',
								icon: 'success',
								duration: 1500
							});

							// 不再标记已经请求过订阅，允许用户多次订阅
							console.log('用户接受订阅消息，但不保存订阅状态，允许多次订阅');
						} else if (res[this.subscribeTemplateId] === 'reject') {
							console.log('用户拒绝订阅消息');
							uni.showToast({
								title: '您已拒绝订阅消息,如果要接收消息请到设置->通知关联->接收',
								icon: 'none',
								duration: 1500
							});
						} else if (res[this.subscribeTemplateId] === 'ban') {
							console.log('用户已被后台封禁');
							uni.showToast({
								title: '订阅功能已被禁用',
								icon: 'none',
								duration: 1500
							});
						} else {
							console.log('其他订阅结果:', res[this.subscribeTemplateId]);
						}
					},
					fail: (err) => {
						console.error('订阅消息失败:', err);
						uni.showToast({
							title: '订阅消息失败',
							icon: 'none',
							duration: 1500
						});
					},
					complete: () => {
						console.log('订阅消息请求完成');
					}
				});
			},

			// 查询报警通知设置
			checkWarningNotifyConfig() {
				// 获取用户ID
				const userId = uni.getStorageSync('userId');
				if (!userId) {
					console.log('未找到用户ID，无法查询报警通知设置');
					return;
				}

				console.log('开始查询报警通知设置，用户ID:', userId);
				// 调用API查询报警通知设置
				this.$http.get(`/warningNotifyConfig?userId=${userId}&page=0&size=10`)
					.then(res => {
						console.log('查询报警通知设置成功:', res);

						// 检查返回的数据结构
						if (res && res.content && Array.isArray(res.content)) {
							console.log('报警通知设置内容数量:', res.content.length);
							// 只有当content数组的长度大于0时才弹出订阅请求
							if (res.content.length > 0) {
								console.log('存在报警通知设置，准备请求订阅');
								// 检查当前登录会话是否已经显示过订阅对话框
								const hasShownSubscribeDialog = uni.getStorageSync('hasShownSubscribeDialogThisLogin');
								if (!hasShownSubscribeDialog) {
									console.log('当前登录会话未显示过订阅对话框，准备显示');

									// 标记当前登录会话已经显示过订阅对话框
									uni.setStorageSync('hasShownSubscribeDialogThisLogin', true);

									// 延迟一下再请求订阅，避免与其他弹窗冲突
									setTimeout(() => {
										// 显示确认对话框
										uni.showModal({
											title: '订阅通知',
											content: '您好，系统配置显示，如果发生报警，需要给您发送通知。请点击下面按钮来确保能正常收到报警信息\n(由于微信限制，每次订阅仅能收到一条消息)',
											success: (res) => {
												if (res.confirm) {
													// 用户点击确定，请求订阅消息
													this.requestSubscribeMessage();
												}
											}
										});
									}, 1000);
								} else {
									console.log('当前登录会话已显示过订阅对话框，不再显示');
								}
							} else {
								console.log('报警通知设置内容为空，不弹出订阅请求');
							}
						}else {
							console.log('未找到有效的报警通知设置数据结构');
						}
					})
					.catch(err => {
						console.error('查询报警通知设置失败:', err);
					});
			},

			// 清除所有storage
			clearAllStorage() {
				// 清除token和登录状态
				uni.removeStorageSync('token');
				uni.removeStorageSync('tokenExpireAt');
				uni.removeStorageSync('isLoggedIn');

				// 清除用户信息
				uni.removeStorageSync('userInfo');
				uni.removeStorageSync('userAdmin');
				uni.removeStorageSync('userGroupAdmin');
				uni.removeStorageSync('userGroupId');
				uni.removeStorageSync('userGroupName');
				uni.removeStorageSync('userId');
				uni.removeStorageSync('userName');
				uni.removeStorageSync('userRealName');

				// 清除角色相关信息
				uni.removeStorageSync('userRoleIdList');
				uni.removeStorageSync('userRoleTypes');
				uni.removeStorageSync('isAdmin');
				uni.removeStorageSync('isOperator');
				uni.removeStorageSync('isGroupAdmin');
				uni.removeStorageSync('isGroupOperator');
				uni.removeStorageSync('isInspector');
				uni.removeStorageSync('isVisitor');

				// 清除记住密码相关信息
				uni.removeStorageSync('username');
				uni.removeStorageSync('password');
				uni.removeStorageSync('rememberPassword');

				// 清除当前登录会话的订阅对话框显示标记
				uni.removeStorageSync('hasShownSubscribeDialogThisLogin');
				console.log('已清除当前登录会话的订阅对话框显示标记');

				console.log('已清除所有storage');
			},

			// 刷新数据
			refreshData() {
				// 重置页码和加载状态
				this.currentPage = 0;
				this.hasMoreData = true;

				// 加载第一页数据
				this.loadData(true);
			},

			// 加载更多数据
			loadMoreData() {
				// 如果没有更多数据或正在加载中，则不执行
				if (!this.hasMoreData || this.isLoading) {
					return;
				}

				// 页码加1，加载下一页数据
				this.currentPage++;
				this.loadData(false);
			},

			// 加载数据
			loadData(isRefresh) {
				// 如果正在加载中，则不执行
				if (this.isLoading) {
					return;
				}

				// 设置加载状态
				this.isLoading = true;

				// 显示加载提示
				if (isRefresh) {
					uni.showLoading({
						title: '刷新中...'
					});
				} else {
					uni.showNavigationBarLoading();
				}

				// 从本地存储获取用户信息
				const userInfo = uni.getStorageSync('userInfo');
				console.log('用户信息:', userInfo);

				// 获取groupId
				const groupId = userInfo?.groupId;
				console.log('获取到的groupId:', groupId);

				// 构建API请求URL
				let url = `/boxCheckRecord?page=${this.currentPage}&size=10&sort=createAt,desc`;

				// 只有当groupId存在时，才添加到URL中
				if (groupId) {
					url += `&groupId=${groupId}`;
					console.log('使用groupId构建URL:', url);
				} else {
					console.log('groupId不存在，不添加到URL中:', url);
				}

				console.log('最终请求URL:', url);

				// 使用请求工具类发送API请求
				this.$http.get(url)
					.then(res => {
						// 请求成功处理
						if (isRefresh) {
							uni.hideLoading();
						} else {
							uni.hideNavigationBarLoading();
						}

						// 重置加载状态
						this.isLoading = false;

						console.log('获取点检列表成功:', res);
						// 如果API返回了点检列表数据，则更新本地数据
						if (res && res.content && Array.isArray(res.content)) {
							// 转换API返回的数据格式为我们需要的格式
							const newData = res.content.map(item => {
								// 打印原始数据，用于调试
								console.log('原始点检记录数据:', item);

								return {
									id: item.id,
									location: item.address || '未知位置',
									status: this.mapApiStatus(item.checkResult), // 使用 mapApiStatus 映射状态
									checkResult: item.checkResult, // 保存原始的 checkResult 值
									inspector: item.checkUserName || item.checker || '未知',
									inspectionTime: item.createAt,
									deviceCount: item.deviceCount || 0,
									remark: item.remark || '',
									attachment: item.attachment || '',
									sn: item.boxSn || ''
								};
							});

							// 如果是刷新，则替换数据，否则追加数据
							if (isRefresh) {
								this.inspections = newData;
							} else {
								this.inspections = [...this.inspections, ...newData];
							}

							// 判断是否还有更多数据
							this.hasMoreData = res.content.length > 0 && !res.last;

							if (isRefresh) {
								uni.showToast({
									title: '数据已更新',
									icon: 'success'
								});
							}
						} else {
							// 没有数据
							this.hasMoreData = false;

							if (isRefresh) {
								this.inspections = [];
								uni.showToast({
									title: '暂无数据',
									icon: 'none'
								});
							}
						}
					})
					.catch(err => {
						// 请求失败处理
						if (isRefresh) {
							uni.hideLoading();
						} else {
							uni.hideNavigationBarLoading();
						}

						// 重置加载状态
						this.isLoading = false;

						console.error('获取点检列表失败:', err);

						// 显示错误提示
						uni.showToast({
							title: '获取数据失败，请稍后重试',
							icon: 'none'
						});
					});
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		width: 100%;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.header-buttons {
		display: flex;
		gap: 20rpx;
	}

	/* 统一按钮基础样式 */
	.btn-action {
		font-size: 28rpx;
		font-weight: bold;
		padding: 16rpx 30rpx;
		border-radius: 10rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	/* 开启点检按钮 */
	.btn-enable {
		background-color: #4CAF50; /* 绿色 */
		color: #fff;
		box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
	}

	/* 关闭点检按钮 */
	.btn-disable {
		background-color: #1976D2; /* 蓝色 */
		color: #fff;
		box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.2);
	}

	/* 点检按钮 */
	.btn-inspect {
		background-color: #1976D2; /* 蓝色 */
		color: #fff;
		box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.2);
	}

	/* 订阅按钮 */
	.btn-subscribe {
		background-color: #4CAF50;
		color: #fff;
		box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
	}

	/* 退出登录按钮 */
	.btn-logout {
		background-color: #f5f5f5;
		color: #333;
	}

	.filter-bar {
		width: 100%;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 20rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.filter-label {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.filter-options {
		display: flex;
		justify-content: space-between;
	}

	.filter-option {
		font-size: 26rpx;
		color: #666;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		background-color: #f5f5f5;
	}

	.filter-option.active {
		background-color: #1976D2;
		color: #fff;
		font-weight: bold;
	}

	.inspection-list {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 30rpx;
		margin-bottom: 40rpx;
	}

	.empty-tip {
		text-align: center;
		padding: 60rpx 0;
		color: #999;
		font-size: 28rpx;
	}

	.inspection-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.inspection-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.inspection-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.inspection-status {
		font-size: 24rpx;
		padding: 6rpx 16rpx;
		border-radius: 30rpx;
	}

	.status-normal {
		background-color: #e6f7e6;
		color: #07c160;
	}

	.status-warning {
		background-color: #fff7e6;
		color: #ff9800;
	}

	.status-danger {
		background-color: #ffe6e6;
		color: #f44336;
	}

	.inspection-info {
		display: flex;
		flex-direction: column;
		padding: 20rpx 0;
		border-top: 2rpx solid #f5f5f5;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.info-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
		padding: 6rpx 0;
	}

	.info-label {
		font-size: 28rpx;
		color: #666;
		flex: 0 0 30%;
		text-align: left;
	}

	.info-value {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		flex: 0 0 68%;
		text-align: right;
		word-break: break-all;
	}

	.inspection-footer {
		padding-top: 20rpx;
		display: flex;
		align-items: flex-start;
	}

	.remark-label {
		font-size: 28rpx;
		color: #666;
		flex: 0 0 15%;
		text-align: left;
	}

	.inspection-remark {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
		flex: 0 0 83%;
		text-align: left;
		word-break: break-all;
	}

	.footer {
		width: 100%;
		display: flex;
		justify-content: center;
		margin-top: 20rpx;
	}

	.btn-primary {
		width: 80%;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 32rpx;
		font-weight: bold;
		background-color: #1976D2;
		color: #fff;
	}

	.load-more {
		width: 100%;
		text-align: center;
		padding: 20rpx 0;
		margin-bottom: 20rpx;
	}

	.load-more text {
		font-size: 26rpx;
		color: #999;
	}

	.refresh-tip {
		width: 100%;
		text-align: center;
		padding: 10rpx 0;
		margin-bottom: 20rpx;
	}

	.refresh-tip text {
		font-size: 26rpx;
		color: #999;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.refresh-tip text::before {
		content: "";
		display: inline-block;
		width: 30rpx;
		height: 30rpx;
		margin-right: 10rpx;
		background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg>');
		background-size: contain;
		background-repeat: no-repeat;
	}
</style>
